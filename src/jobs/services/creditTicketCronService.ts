import { CreditTicket, CreditTicketDocument } from "../../models/CreditTicket";
import Decimal from "decimal.js";
import CreditTicketService from "../../services/creditTicketService";
import { ProviderEnum } from "../../configs/providersConfig";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { DepositCashTransaction } from "../../models/Transaction";
import { DepositMethodEnum } from "../../types/transactions";
import DateUtil from "../../utils/dateUtil";

class CreditTicketCronService {
  /**
   * PUBLIC METHODS
   */

  /**
   * @description Syncs all credit tickets that have pending WK internal transfers.
   */
  public async syncCreditTicketInternalTransfers(): Promise<void> {
    // Sync credit tickets that were submitted at least 15 minutes ago, to act as a fallback to webhook mechanism
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

    const creditTicketsWithPendingInternalTransfers = await CreditTicket.find({
      status: "Pending",
      "deposit.providers.wealthkernel.status": { $in: ["Requested", "Accepted"] },
      "deposit.providers.wealthkernel.id": { $exists: true },
      "deposit.providers.wealthkernel.submittedAt": { $lt: fifteenMinutesAgo }
    }).populate("portfolio");

    for (let i = 0; i < creditTicketsWithPendingInternalTransfers.length; i++) {
      const creditTicket = creditTicketsWithPendingInternalTransfers[i];

      await CreditTicketService.syncCreditTicketWithPendingInternalTransferSafely(creditTicket);
    }
  }

  /**
   * @description Creates WK internal transfers for all credit tickets that have been created and have no submitted transfers yet.
   */
  public async createCreditTicketInternalTransfers(): Promise<void> {
    // Create internal transfers for credit tickets that were createdAt at least 15 minutes ago, to act as a fallback to webhook mechanism
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

    const creditTicketsWithUnsubmittedInternalTransfers: CreditTicketDocument[] = await CreditTicket.find({
      status: "Pending",
      "deposit.activeProviders": ProviderEnum.WEALTHKERNEL,
      createdAt: { $lt: fifteenMinutesAgo },
      $or: [
        {
          "deposit.providers.wealthkernel.id": {
            $exists: false
          }
        },
        {
          "deposit.providers.wealthkernel.id": {
            $eq: undefined
          }
        }
      ]
    }).populate("portfolio");

    for (let i = 0; i < creditTicketsWithUnsubmittedInternalTransfers.length; i++) {
      await CreditTicketService.createInternalTransferForCreditTicketSafely(
        creditTicketsWithUnsubmittedInternalTransfers[i]
      );
    }
  }

  /**
   * For credit tickets reporting, we report three main pieces of information:
   * 1. How much open credit amount have, currently.
   * 2. Whether there are any open (Pending or Credited) credit tickets for more than 2 business days.
   * 3. Whether the total EU deposit amount for the last two business days matches the credited (non-Rejected) amount.
   */
  public async reportOpenCredit(): Promise<void> {
    const twoBusinessDaysAgo = DateUtil.getStartOfDay(DateUtil.getDateNWorkDaysAgo(new Date(Date.now()), 2));

    const [creditedTickets, oldOpenTickets, recentCreditedTickets, recentDeposits] = await Promise.all([
      CreditTicket.find({
        status: "Credited"
      }),
      CreditTicket.find({
        status: { $in: ["Pending", "Credited"] },
        createdAt: { $lt: twoBusinessDaysAgo }
      }),
      CreditTicket.find({
        createdAt: { $gte: twoBusinessDaysAgo },
        status: { $in: ["Credited", "Settled"] }
      }),
      DepositCashTransaction.find({
        createdAt: { $gte: twoBusinessDaysAgo },
        depositMethod: DepositMethodEnum.BANK_TRANSFER
      })
    ]);

    const totalCreditedAmount = creditedTickets
      .map(({ consideration }) => consideration.amount)
      .reduce((sum: Decimal, value: number) => sum.plus(value), new Decimal(0))
      .div(100);
    const totalRecentDeposits = recentDeposits
      .map(({ consideration }) => consideration.amount)
      .reduce((sum: Decimal, value: number) => sum.plus(value), new Decimal(0))
      .div(100);
    const totalRecentCredited = recentCreditedTickets
      .map(({ consideration }) => consideration.amount)
      .reduce((sum: Decimal, value: number) => sum.plus(value), new Decimal(0))
      .div(100);

    eventEmitter.emit(events.creditTickets.creditedAmountReported.eventId, {
      totalCreditedAmount,
      oldOpenTickets: oldOpenTickets.length,
      depositVsCreditedMatch: totalRecentDeposits.equals(totalRecentCredited),
      totalRecentDeposits,
      totalRecentCredited
    });
  }
}

export default new CreditTicketCronService();
