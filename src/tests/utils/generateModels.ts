import {
  MonitorType,
  TransactionMonitor,
  TransactionMonitorDocument,
  TransactionMonitorDTOInterface
} from "../../models/TransactionMonitor";
import { faker } from "@faker-js/faker";
import mongoose from "mongoose";
import { customAlphabet } from "nanoid";
import {
  countriesConfig,
  entitiesConfig,
  fees,
  indexesConfig,
  investmentUniverseConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import { Account, AccountDocument, AccountDTOInterface } from "../../models/Account";
import { Address, AddressDocument, AddressDTOInterface } from "../../models/Address";
import { BankAccount, BankAccountDocument, BankAccountInterface } from "../../models/BankAccount";
import {
  DailyPortfolioSavingsTicker,
  DailyPortfolioSavingsTickerDocument,
  DailyPortfolioTicker,
  DailyPortfolioTickerDocument,
  DailySavingsProductTicker,
  DailySavingsProductTickerDocument
} from "../../models/DailyTicker";
import {
  IntraDayAssetTicker,
  IntraDayAssetTickerDocument,
  IntraDayAssetTickerDTOInterface,
  IntraDayPortfolioTicker,
  IntraDayPortfolioTickerDocument,
  IntraDayPortfolioTickerDTOInterface
} from "../../models/IntraDayTicker";
import { InvestmentProduct, InvestmentProductDocument } from "../../models/InvestmentProduct";
import { Order, OrderDocument, OrderDTOInterface, OrderSubmissionIntentEnum } from "../../models/Order";
import { Participant, ParticipantDocument, ParticipantInterface } from "../../models/Participant";
import {
  HoldingsType,
  Portfolio,
  PortfolioDocument,
  PortfolioInterface,
  PortfolioModeEnum
} from "../../models/Portfolio";
import { Reward, RewardDocument, RewardInterface } from "../../models/Reward";
import {
  AssetDividendTransaction,
  AssetDividendTransactionDocument,
  AssetDividendTransactionInterface,
  AssetTransaction,
  AssetTransactionDocument,
  AssetTransactionInterfaceDTO,
  CashbackTransaction,
  CashbackTransactionDocument,
  ChargeTransaction,
  ChargeTransactionDocument,
  ChargeTransactionInterface,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  DividendTransaction,
  DividendTransactionDocument,
  DividendTransactionInterface,
  PortfolioTransactionCategoryType,
  RebalanceTransaction,
  RebalanceTransactionDocument,
  RevertRewardTransaction,
  RevertRewardTransactionDocument,
  SavingsDividendTransaction,
  SavingsDividendTransactionDocument,
  SavingsDividendTransactionDTOInterface,
  SavingsTopupTransaction,
  SavingsTopupTransactionDocument,
  SavingsTopupTransactionDTOInterface,
  SavingsWithdrawalTransaction,
  SavingsWithdrawalTransactionDocument,
  SavingsWithdrawalTransactionDTOInterface,
  StockSplitTransaction,
  StockSplitTransactionDocument,
  WealthyhoodDividendTransaction,
  WealthyhoodDividendTransactionDocument,
  WealthyhoodDividendTransactionDTOInterface,
  WithdrawalCashTransaction,
  WithdrawalCashTransactionDocument,
  WithdrawalCashTransactionInterface
} from "../../models/Transaction";
import { User, UserDocument, UserInterface, UserTypeEnum } from "../../models/User";
import { MINIMUM_REWARD_DEPOSIT } from "../../services/rewardService";
import { Subscription, SubscriptionDocument, SubscriptionInterface } from "../../models/Subscription";
import {
  UserDataRequest,
  UserDataRequestDocument,
  UserDataRequestInterface,
  UserDataRequestReasonEnum
} from "../../models/UserDataRequest";
import { Gift, GiftDocument, GiftInterface } from "../../models/Gift";
import { Mandate, MandateDocument, MandateInterface } from "../../models/Mandate";
import {
  LifetimeEnum,
  ReferralCode,
  ReferralCodeDocument,
  ReferralCodeDTOInterface,
  ReferralCodeInterface
} from "../../models/ReferralCode";
import DateUtil from "../../utils/dateUtil";
import {
  AutomationDTOInterface,
  RebalanceAutomation,
  RebalanceAutomationDocument,
  SavingsTopUpAutomation,
  SavingsTopUpAutomationDocument,
  SavingsTopUpAutomationDTOInterface,
  TopUpAutomation,
  TopUpAutomationDocument
} from "../../models/Automation";
import {
  RewardInvitation,
  RewardInvitationDocument,
  RewardInvitationInterface
} from "../../models/RewardInvitation";
import { KycOperation, KycOperationDocument, KycOperationDTOInterface } from "../../models/KycOperation";
import { ProviderEnum } from "../../configs/providersConfig";
import {
  PaymentMethod,
  PaymentMethodBrandEnum,
  PaymentMethodDocument,
  PaymentMethodTypeEnum
} from "../../models/PaymentMethod";
import {
  RiskAssessment,
  RiskAssessmentDocument,
  RiskAssessmentDTOInterface,
  SourceOfFundsEnum
} from "../../models/RiskAssessment";
import { SavingsProduct, SavingsProductDocument, SavingsProductInterface } from "../../models/SavingsProduct";
import {
  AssetNews,
  AssetNewsDocument,
  AssetNewsDTOInterface,
  AssetNewsSentimentEnum
} from "../../models/AssetNews";
import { Wallet, WalletDocument, WalletDTOInterface } from "../../models/Wallet";
import { AmlScreeningResultEnum } from "../../configs/riskAssessmentConfig";
import { Requisition, RequisitionDocument, RequisitionDTOInterface } from "../../models/Requisition";
import { Payout, PayoutDocument, PayoutDTOInterface } from "../../models/Payout";
import { AppRating, AppRatingDocument, AppRatingDTOInterface } from "../../models/AppRating";
import {
  AssetIsinChangeCorporateEvent,
  AssetIsinChangeCorporateEventDocument,
  AssetIsinChangeCorporateEventDTOInterface,
  StockSplitCorporateEvent,
  StockSplitCorporateEventDocument,
  StockSplitCorporateEventDTOInterface
} from "../../models/CorporateEvent";
import {
  ContentEntry,
  ContentEntryCategoryEnum,
  ContentEntryContentTypeEnum,
  ContentEntryDocument,
  ContentEntryDTOInterface
} from "../../models/ContentEntry";
import { FinimizeContentTypeEnum } from "../../external-services/finimizeService";
import {
  AppNotificationSettingEnum,
  AppNotificationSettings,
  EmailNotificationSettingEnum,
  EmailNotificationSettings,
  NotificationSettings,
  NotificationSettingsDocument,
  NotificationSettingsDTOInterface
} from "../../models/NotificationSettings";
import {
  DailySummarySnapshot,
  DailySummarySnapshotDocument,
  DailySummarySnapshotDTOInterface,
  IndividualSentimentScoreComponentEnum,
  TotalSentimentScoreComponentEnum
} from "../../models/DailySummarySnapshot";
import { SundownDigest, SundownDigestDocument, SundownDigestDTOInterface } from "../../models/SundownDigest";
import { Notification, NotificationDocument, NotificationDTOInterface } from "../../models/Notification";
import { LearningNotificationEventEnum } from "../../event-handlers/notificationEvents";
import { IndexPrice, IndexPriceDocument, IndexPriceDTOInterface } from "../../models/IndexPrice";
import Decimal from "decimal.js/decimal";
import { CreditTicket, CreditTicketDocument, CreditTicketDTOInterface } from "../../models/CreditTicket";
import { LearnNews, LearnNewsDocument, LearnNewsDTOInterface } from "../../models/LearnNews";
import {
  InvoiceReferenceNumber,
  InvoiceReferenceNumberDocument,
  InvoiceReferenceNumberInterface
} from "../../models/InvoiceReferenceNumber";
import { DepositMethodEnum } from "../../types/transactions";

const { ASSET_CONFIG, AssetArrayConst, InvestmentGeographyArray } = investmentUniverseConfig;
const { SavingsProductArray } = savingsUniverseConfig;
const { MINIMUM_FX_FEE, MINIMUM_COMMISSION_FEE } = fees;
const BANK_REFERENCE_LENGTH = 16;
const ALPHABET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-";
const MONGOOSE_OBJECT_ID_LENGTH = 12;
const lowerCaseAlphabet = "0123456789abcdefghijklmnopqrstuvwxyz";
const nanoidShort = customAlphabet(lowerCaseAlphabet, 8);

/**
 * GENERATE MODEL DATA
 */

async function buildAddress(overrides: Partial<AddressDTOInterface> = {}): Promise<AddressDocument> {
  const addressData: AddressDTOInterface = {
    owner: new mongoose.Types.ObjectId(),
    line1: faker.location.streetAddress(),
    city: faker.location.city(),
    countryCode: faker.helpers.arrayElement(countriesConfig.countryCodesArray),
    activeProviders: [ProviderEnum.WEALTHKERNEL],
    postalCode: "SE11EL",
    ...overrides
  };
  return new Address(addressData).save();
}

async function buildAccount(overrides: Partial<AccountDTOInterface> = {}): Promise<AccountDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }

  return new Account({
    wrapperType: "GIA",
    name: "General Investment Account",
    owner: owner?.id,
    activeProviders: [ProviderEnum.WEALTHKERNEL],
    ...overrides
  }).save();
}

async function buildBankAccount(overrides: Partial<BankAccountInterface> = {}): Promise<BankAccountDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser({}, false);
  }

  return new BankAccount({
    name: faker.finance.accountName(),
    number: faker.finance.accountNumber(),
    sortCode: "00-00-00",
    owner: owner?.id,
    active: true,
    activeProviders: [
      ProviderEnum.WEALTHKERNEL,
      ProviderEnum.GOCARDLESS,
      ProviderEnum.TRUELAYER,
      ProviderEnum.WEALTHYHOOD
    ],
    ...overrides
  }).save();
}

/**
 * Builds a fake User DTO with all the necessary properties to create a Wealthkernel
 * party (EXCEPT the bank account property), and merges its properties with the
 * properties of the object parameter.
 *
 * @param overrides The object which will override the default property values.
 *
 * @returns A fake User DTO with all the necessary properties to create a Wealthkernel
 * party (EXCEPT the bank account property), unless overriden by the object parameter.
 */
function buildUserDTO(overrides: Partial<UserInterface> = {}): Partial<UserInterface> {
  return {
    email: faker.internet.email(),
    emailDisposable: false,
    auth0: {
      id: faker.string.uuid()
    },
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    emailVerified: true,
    role: [UserTypeEnum.INVESTOR],
    dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" }),
    submittedRequiredInfoAt: faker.date.between({ from: "2022-01-01", to: "2022-01-05" }),
    nationalities: ["GB"],
    taxResidency: {
      countryCode: "GB",
      proofType: "NINO",
      value: "*********"
    },
    employmentInfo: {
      incomeRangeId: "3",
      annualIncome: {
        amount: 25000,
        currency: "GBP"
      },
      employmentStatus: "FullTime",
      industry: "Tobacco",
      sourcesOfWealth: ["Gift"]
    },
    residencyCountry: "GB",
    currency: "GBP",
    companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
    lastLogin: faker.date.between({ from: "2021-01-01", to: "2022-01-05" }),
    activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.STRIPE],
    isPassportVerified: true,
    deviceTokens: { ios: faker.string.uuid(), android: faker.string.uuid() },
    ...overrides
  };
}

/**
 * Builds a fake User DTO with all the necessary properties to create a Wealthkernel
 * party (INCLUDING the bank account property), and merges its properties with the
 * properties of the object parameter.
 *
 * @param overrides The object which will override the default property values.
 * @param withBankAccount
 *
 * @param withoutPassport
 * @returns A fake User DTO with all the necessary properties to create a Wealthkernel
 * party (INCLUDING the bank account property), unless overriden by the object parameter.
 */
async function buildUser(
  overrides: Partial<UserInterface> = {},
  withBankAccount = true,
  withoutPassport = false
): Promise<UserDocument> {
  let userDTO = buildUserDTO({ ...overrides });

  // without passport
  if (withoutPassport) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { firstName, lastName, dateOfBirth, nationalities, ...userWithoutPassport } = userDTO;
    userDTO = userWithoutPassport;
  }

  const user = await new User(userDTO).save();
  // Add bank account
  if (withBankAccount) {
    await buildBankAccount({ owner: user.id });
    await user.populate("bankAccounts");
  }
  return user;
}

async function buildDailyPortfolioTicker({ ...overrides }: any = {}): Promise<DailyPortfolioTickerDocument> {
  let portfolio: PortfolioDocument | undefined;
  if (!overrides.portfolio) {
    portfolio = await buildPortfolio();
  }

  return new DailyPortfolioTicker({
    currency: "GBP",
    openingPricePerCurrency: { GBP: faker.number.int({ min: 1, max: 1000 }) },
    closingPricePerCurrency: { GBP: faker.number.int({ min: 1, max: 1000 }) },
    pricePerCurrency: { GBP: faker.number.int({ min: 2, max: 1000 }) },
    portfolio: portfolio?.id,
    returnPercentage: faker.number.int({ min: 0, max: 1 }),
    ...overrides
  }).save();
}

async function buildDailyPortfolioTickersBulk(
  portfolio: PortfolioDocument,
  prices: number[],
  overrides: Partial<DailyPortfolioTickerDocument> = {}
): Promise<void> {
  const dbOperations = prices.map((price, index) => {
    const date = new Date();
    date.setDate(date.getDate() - index); // Create tickers for past days

    return {
      updateOne: {
        filter: { portfolio: portfolio.id, date },
        update: {
          $setOnInsert: {
            currency: "GBP",
            openingPricePerCurrency: { GBP: price, EUR: price, USD: price },
            closingPricePerCurrency: { GBP: price, EUR: price, USD: price },
            pricePerCurrency: { GBP: price, EUR: price, USD: price },
            portfolio: portfolio.id,
            returnPercentage: 0, // Since we're using the same price, return is 0
            date,
            ...overrides
          }
        },
        upsert: true
      }
    };
  });

  await DailyPortfolioTicker.bulkWrite(dbOperations);
}

async function buildIntraDayAssetTicker({
  ...overrides
}: Partial<IntraDayAssetTickerDTOInterface> = {}): Promise<IntraDayAssetTickerDocument> {
  return new IntraDayAssetTicker({
    currency: "GBP",
    timestamp: new Date(),
    pricePerCurrency: { GBP: faker.number.int({ min: 2, max: 1000 }) },
    ...overrides
  }).save();
}

async function buildIntraDayPortfolioTicker({
  ...overrides
}: Partial<IntraDayPortfolioTickerDTOInterface> = {}): Promise<IntraDayPortfolioTickerDocument> {
  return new IntraDayPortfolioTicker({
    currency: "GBP",
    timestamp: new Date(),
    pricePerCurrency: { GBP: faker.number.int({ min: 2, max: 1000 }) },
    ...overrides
  }).save();
}

async function buildIntraDayPortfolioTickersBulk(
  portfolio: PortfolioDocument,
  prices: number[],
  overrides: Partial<IntraDayPortfolioTickerDTOInterface> = {}
): Promise<void> {
  const MINUTES_INTERVAL = 10;
  const MINUTES_IN_DAY = 24 * 60;
  const dbOperations = prices.map((price, index) => {
    const timestamp = new Date();
    // Calculate days and minutes to subtract
    const totalMinutesToSubtract = new Decimal(index).mul(MINUTES_INTERVAL);
    const daysToSubtract = totalMinutesToSubtract.div(MINUTES_IN_DAY).floor().toNumber();
    const minutesToSubtract = totalMinutesToSubtract.mod(MINUTES_IN_DAY).toNumber();

    // Set the date
    timestamp.setDate(timestamp.getDate() - daysToSubtract);
    timestamp.setMinutes(timestamp.getMinutes() - minutesToSubtract);

    return {
      updateOne: {
        filter: { portfolio: portfolio.id, timestamp },
        update: {
          $setOnInsert: {
            currency: "GBP",
            timestamp,
            pricePerCurrency: { GBP: price, EUR: price, USD: price },
            portfolio: portfolio.id,
            ...overrides
          }
        },
        upsert: true
      }
    };
  });

  await IntraDayPortfolioTicker.bulkWrite(dbOperations);
}

async function buildStockSplitCorporateEvent({
  ...overrides
}: Partial<StockSplitCorporateEventDTOInterface> = {}): Promise<StockSplitCorporateEventDocument> {
  return new StockSplitCorporateEvent({
    asset: faker.helpers.arrayElement(AssetArrayConst),
    date: new Date(),
    splitRatio: "3.000000/1.000000",
    ...overrides
  }).save();
}

async function buildAssetIsinChangeCorporateEvent({
  ...overrides
}: Partial<AssetIsinChangeCorporateEventDTOInterface> = {}): Promise<AssetIsinChangeCorporateEventDocument> {
  return new AssetIsinChangeCorporateEvent({
    asset: faker.helpers.arrayElement(AssetArrayConst),
    date: new Date(),
    oldISIN: "US5949181045",
    newISIN: "US5949181045",
    ...overrides
  }).save();
}

async function buildInvestmentProduct(
  createTicker = true,
  {
    assetId = faker.helpers.arrayElement(AssetArrayConst),
    price = faker.number.int({ min: 1, max: 1000 }),
    ...overrides
  }: any = {},
  dailyTickerOverrides: any = {}
): Promise<InvestmentProductDocument> {
  const investmentProductDocument = new InvestmentProduct({
    assetClass: faker.helpers.arrayElement(["EQUITIES", "BONDS", "COMMODITIES", "REAL_ESTATE"]),
    commonId: assetId,
    listed: true,
    buyLine: { active: true },
    sellLine: { active: true },
    ...overrides
  });
  const investmentProduct = await investmentProductDocument.save();
  if (createTicker) {
    await buildIntraDayAssetTicker({
      investmentProduct: investmentProductDocument.id,
      pricePerCurrency: { GBP: price, EUR: price, USD: price },
      ...dailyTickerOverrides
    });
    await investmentProduct.populate("currentTicker");
  }

  return investmentProduct;
}

async function buildHoldingDTO(
  save = false,
  assetCommonId: investmentUniverseConfig.AssetType = faker.helpers.arrayElement(AssetArrayConst),
  quantity = faker.number.int(),
  investmentProductOverrides: any = {},
  dailyTickerOverrides: any = {}
): Promise<HoldingsType> {
  return {
    asset: await buildInvestmentProduct(
      save,
      { assetId: assetCommonId, ...investmentProductOverrides },
      dailyTickerOverrides
    ),
    assetCommonId,
    quantity
  };
}

function buildPortfolioDTO(overrides: any = {}): any {
  const defaultMode = PortfolioModeEnum.REAL;
  const mode = overrides.mode || defaultMode;

  return {
    account: mode === PortfolioModeEnum.REAL ? new Account().id : null,
    cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
    holdings: [],
    initialHoldingsAllocation: [] as HoldingsType[],
    mode,
    personalisationPreferences: {
      assetClasses: ["equities"],
      geography: faker.helpers.arrayElement(InvestmentGeographyArray),
      risk: faker.number.int({ min: 0, max: 1 }),
      sectors: []
    },
    activeProviders: [ProviderEnum.WEALTHKERNEL],
    ...overrides
  };
}

async function buildPortfolio(
  overrides: Partial<PortfolioInterface> = {},
  withAccount = true
): Promise<PortfolioDocument> {
  const defaultMode = PortfolioModeEnum.REAL;
  const mode = overrides.mode || defaultMode;

  let user: UserDocument | undefined;
  if (!overrides.owner) {
    user = await buildUser();
  }
  let account: AccountDocument | undefined;
  if (withAccount && !overrides.account && mode === PortfolioModeEnum.REAL) {
    account = await buildAccount({ owner: user?.id || overrides.owner });
  }
  return new Portfolio(buildPortfolioDTO({ owner: user?.id, account: account?.id, mode, ...overrides })).save();
}

function buildAssetTransactionDTO({
  user = new User(buildUserDTO()),
  ...overrides
}: any = {}): AssetTransactionInterfaceDTO {
  const amount = faker.number.int();
  const portfolioTransactionCategory = (overrides.portfolioTransactionCategory ??
    "buy") as PortfolioTransactionCategoryType;

  return {
    consideration: {
      currency: "GBP",
      amount
    },
    owner: user.id,
    portfolio: new Portfolio(buildPortfolioDTO()),
    portfolioTransactionCategory,
    originalInvestmentAmount: portfolioTransactionCategory !== "update" ? amount : undefined,
    createdAt: new Date(),
    ...overrides
  };
}

async function buildAssetTransaction(
  overrides: Partial<AssetTransactionInterfaceDTO> = {}
): Promise<AssetTransactionDocument> {
  return new AssetTransaction(buildAssetTransactionDTO(overrides)).save();
}

async function buildDividendTransaction(
  overrides: Partial<DividendTransactionInterface> = {}
): Promise<DividendTransactionDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }
  let portfolio: PortfolioDocument | undefined;
  if (!overrides.portfolio) {
    portfolio = await buildPortfolio();
  }

  const assetId = faker.helpers.arrayElement(AssetArrayConst);
  return new DividendTransaction({
    consideration: {
      currency: "GBP",
      amount: faker.number.int()
    },
    owner: owner?.id,
    portfolio: portfolio?.id,
    asset: assetId,
    isin: ASSET_CONFIG[assetId].isin,
    createdAt: new Date(),
    activeProviders: [ProviderEnum.WEALTHKERNEL],
    ...overrides
  }).save();
}

async function buildAssetDividendTransaction(
  overrides: Partial<AssetDividendTransactionInterface> = {}
): Promise<AssetDividendTransactionDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }
  let portfolio: PortfolioDocument | undefined;
  if (!overrides.portfolio) {
    portfolio = await buildPortfolio();
  }

  const assetId = faker.helpers.arrayElement(AssetArrayConst);
  return new AssetDividendTransaction({
    quantity: faker.number.float({ min: 0, max: 1, multipleOf: 0.000000001 }),
    owner: owner?.id,
    portfolio: portfolio?.id,
    asset: assetId,
    isin: ASSET_CONFIG[assetId].isin,
    createdAt: new Date(),
    activeProviders: [ProviderEnum.WEALTHKERNEL],
    ...overrides
  }).save();
}

async function buildCashbackTransaction(overrides: any = {}): Promise<CashbackTransactionDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }
  let portfolio: PortfolioDocument | undefined;
  if (!overrides.portfolio) {
    portfolio = await buildPortfolio();
  }

  return new CashbackTransaction({
    consideration: {
      currency: "GBP",
      amount: faker.number.int()
    },
    cashbackMonth: DateUtil.getYearAndMonth(new Date()),
    price: "paid_low_monthly",
    owner: owner?.id,
    portfolio: portfolio?.id,
    createdAt: new Date(),
    deposit: { activeProviders: [ProviderEnum.WEALTHKERNEL] },
    ...overrides
  }).save();
}

async function buildStockSplitTransaction(overrides: any = {}): Promise<StockSplitTransactionDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }
  let portfolio: PortfolioDocument | undefined;
  if (!overrides.portfolio) {
    portfolio = await buildPortfolio();
  }

  return new StockSplitTransaction({
    consideration: {
      currency: "GBP"
    },
    owner: owner?.id,
    portfolio: portfolio?.id,
    createdAt: new Date(),
    ...overrides
  }).save();
}

async function buildChargeTransaction(
  overrides: Partial<ChargeTransactionInterface> = {}
): Promise<ChargeTransactionDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }
  let portfolio: PortfolioDocument | undefined;
  if (!overrides.portfolio) {
    portfolio = await buildPortfolio();
  }

  const considerationAmount = overrides.consideration?.amount ?? faker.number.int();
  return new ChargeTransaction({
    chargeMethod: "cash",
    chargeType: "subscription",
    consideration: {
      currency: "GBP",
      amount: considerationAmount
    },
    owner: owner?.id,
    portfolio: portfolio?.id,
    createdAt: new Date(),
    chargeMonth: DateUtil.getYearAndMonth(new Date()),
    originalChargeAmount: considerationAmount,
    activeProviders: [ProviderEnum.WEALTHKERNEL],
    ...overrides
  }).save();
}

async function buildRevertRewardTransaction(overrides: any = {}): Promise<RevertRewardTransactionDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }
  let portfolio: PortfolioDocument | undefined;
  if (!overrides.portfolio) {
    portfolio = await buildPortfolio();
  }

  const considerationAmount = overrides.consideration?.amount ?? faker.number.int();
  return new RevertRewardTransaction({
    consideration: {
      currency: "GBP",
      amount: considerationAmount
    },
    owner: owner?.id,
    portfolio: portfolio?.id,
    createdAt: new Date(),
    activeProviders: [ProviderEnum.WEALTHKERNEL],
    ...overrides
  }).save();
}

async function buildRebalanceTransaction(overrides: any = {}): Promise<RebalanceTransactionDocument> {
  return new RebalanceTransaction({
    consideration: {
      currency: "GBP"
    },
    owner: new User(buildUserDTO()),
    portfolio: new Portfolio(buildPortfolioDTO()),
    targetAllocation: [] as HoldingsType[],
    createdAt: new Date(),
    ...overrides
  }).save();
}

async function buildOrder(overrides: Partial<OrderDTOInterface> = {}): Promise<OrderDocument> {
  return new Order({
    status: "Pending",
    transaction: faker.string.uuid(),
    consideration: {
      originalAmount: faker.number.int({ min: 1, max: 1000 }),
      amount: faker.number.int({ min: 1, max: 1000 })
    },
    isin: faker.string.uuid(),
    side: Math.random() < 0.5 ? "Sell" : "Buy",
    activeProviders: [ProviderEnum.WEALTHKERNEL],
    submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
    ...overrides
  }).save();
}

async function buildDepositCashTransaction(
  overrides: Partial<DepositCashTransactionDocument> = {},
  existingUser?: UserDocument,
  existingPortfolio?: PortfolioDocument
): Promise<DepositCashTransactionDocument> {
  const user = existingUser || (await buildUser());
  const portfolio =
    existingPortfolio ||
    (await buildPortfolio({
      owner: user.id,
      providers: {
        wealthkernel: {
          id: faker.string.uuid(),
          status: "Active"
        }
      }
    }));

  const nanoid = customAlphabet(ALPHABET, BANK_REFERENCE_LENGTH);
  const initialDeposit = new DepositCashTransaction({
    depositMethod: DepositMethodEnum.OPEN_BANKING,
    bankReference: nanoid(),
    consideration: {
      currency: "GBP",
      amount: faker.number.int()
    },
    owner: user,
    portfolio,
    activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.TRUELAYER],
    createdAt: new Date(),
    status: "Pending",
    ...overrides
  });

  return initialDeposit.save();
}

async function buildParticipant(overrides: Partial<ParticipantInterface> = {}): Promise<ParticipantDocument> {
  return new Participant({
    email: faker.internet.email(),
    ...overrides
  }).save();
}

async function buildSubscription(overrides: Partial<SubscriptionInterface> = {}): Promise<SubscriptionDocument> {
  return new Subscription({
    category: "FeeBasedSubscription",
    price: "free_monthly",
    ...overrides
  }).save();
}

async function buildUserDataRequest(
  overrides: Partial<UserDataRequestInterface> = {}
): Promise<UserDataRequestDocument> {
  return new UserDataRequest({
    reason: UserDataRequestReasonEnum.USER_REQUEST,
    ...overrides
  }).save();
}

async function buildReward(overrides: Partial<RewardInterface> = {}): Promise<RewardDocument> {
  let referrer: mongoose.Types.ObjectId | undefined;
  if (!overrides.referrer && !overrides.referralCampaign) {
    const user = await buildUser();
    referrer = user.id;
  }

  let referral: mongoose.Types.ObjectId | undefined;
  if (!overrides.referrer) {
    const user = await buildUser();
    referral = user.id;
  }

  let targetUser: mongoose.Types.ObjectId | undefined;
  if (!overrides.targetUser) {
    targetUser = (referrer || overrides.referrer) as mongoose.Types.ObjectId;
  }

  const assetId = faker.helpers.arrayElement(AssetArrayConst);
  const rewardData: any = {
    asset: assetId,
    isin: ASSET_CONFIG[assetId].isin,
    status: "Pending",
    consideration: {
      currency: "GBP",
      // the total value of the ETF that we'll reward - stored in cents
      amount: faker.number.int({ min: MINIMUM_REWARD_DEPOSIT })
    },
    fees: {
      fx: {
        amount: faker.number.int({ min: MINIMUM_FX_FEE, max: 1 }),
        currency: "GBP"
      },
      commission: {
        amount: faker.number.int({ min: MINIMUM_COMMISSION_FEE, max: 1 }),
        currency: "GBP"
      }
    },
    deposit: {
      activeProviders: [ProviderEnum.WEALTHKERNEL]
    },
    order: {
      activeProviders: [ProviderEnum.WEALTHKERNEL]
    },
    referrer,
    referral,
    targetUser,
    ...overrides
  };

  return new Reward(rewardData).save();
}

async function buildRewardInvitation(
  overrides: Partial<RewardInvitationInterface> = {}
): Promise<RewardInvitationDocument> {
  let referrer: mongoose.Types.ObjectId | undefined;
  if (!overrides.referrer) {
    const user = await buildUser();
    referrer = user.id;
  }

  const rewardInvitationData: any = {
    referrer,
    targetUser: overrides.targetUserEmail ?? faker.internet.email(),
    ...overrides
  };

  return new RewardInvitation(rewardInvitationData).save();
}

async function buildGift(overrides: Partial<GiftInterface> = {}): Promise<GiftDocument> {
  let gifter: mongoose.Types.ObjectId | undefined;
  if (!overrides.gifter) {
    const user = await buildUser();
    gifter = user.id;
  }

  let targetUserEmail: string;
  if (!overrides.targetUserEmail) {
    const user = await buildUser();
    targetUserEmail = user.email;
  }

  const giftData: any = {
    consideration: {
      currency: "GBP",
      amount: 2000
    },
    message: faker.lorem.words(5),
    gifter,
    targetUserEmail,
    deposit: {
      activeProviders: [ProviderEnum.WEALTHKERNEL]
    },
    ...overrides
  };

  return new Gift(giftData).save();
}

async function buildMandate(overrides: Partial<MandateInterface> = {}): Promise<MandateDocument> {
  let owner: mongoose.Types.ObjectId | undefined;
  let bankAccount: mongoose.Types.ObjectId | undefined;
  if (!overrides.owner) {
    const user = await buildUser();
    owner = user.id;
    bankAccount = user.bankAccounts[0].id;
  }

  const mandateData: Partial<MandateInterface> = {
    owner: owner,
    bankAccount: bankAccount,
    category: "Subscription",
    activeProviders: [ProviderEnum.GOCARDLESS],
    ...overrides
  };

  return new Mandate(mandateData).save();
}

async function buildDailySummarySnapshot(
  overrides: Partial<DailySummarySnapshotDTOInterface> = {}
): Promise<DailySummarySnapshotDocument> {
  let owner: mongoose.Types.ObjectId | undefined;
  if (!overrides?.metadata?.owner) {
    const user = await buildUser();
    owner = user.id;
  }

  const data: Partial<DailySummarySnapshotDTOInterface> = {
    metadata: {
      owner: owner
    },
    date: new Date(Date.now()),
    sentimentScore: {
      [TotalSentimentScoreComponentEnum.TOTAL]: faker.number.float({ min: 0, max: 1 }),
      [IndividualSentimentScoreComponentEnum.NEWS]: faker.number.float({ min: 0, max: 1 }),
      [IndividualSentimentScoreComponentEnum.ANALYST]: faker.number.float({ min: 0, max: 1 }),
      [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: faker.number.float({ min: 0, max: 1 })
    },
    portfolio: {
      cash: {
        value: { amount: faker.number.int({ min: 1000, max: 5000 }), currency: "GBP" }
      },
      holdings: {
        value: { amount: faker.number.int({ min: 1000, max: 5000 }), currency: "GBP" },
        dailyUpBy: faker.number.float({ min: -100, max: 100 }),
        dailyReturnPercentage: faker.number.float({ min: -1, max: 1 }),
        assets: [
          {
            assetId: faker.helpers.arrayElement(AssetArrayConst),
            quantity: faker.number.float({ min: 1, max: 10 }),
            latestPrice: { amount: faker.number.int({ min: 100, max: 200 }), currency: "GBP" },
            holdingWeightPercentage: 1,
            dailyReturnPercentage: faker.number.float({ min: -1, max: 1 })
          }
        ]
      },
      savings: {
        value: { amount: faker.number.int({ min: 1000, max: 5000 }), currency: "GBP" }
      },
      total: {
        value: { amount: faker.number.int({ min: 1000, max: 5000 }), currency: "GBP" }
      }
    },
    ...overrides
  };

  return new DailySummarySnapshot(data).save();
}

async function buildPaymentMethod(overrides: Partial<PaymentMethodDocument> = {}): Promise<PaymentMethodDocument> {
  let owner: mongoose.Types.ObjectId | undefined;
  if (!overrides.owner) {
    const user = await buildUser();
    owner = user.id;
  }

  const paymentMethodData: Partial<PaymentMethodDocument> = {
    owner: owner,
    type: PaymentMethodTypeEnum.CARD,
    brand: PaymentMethodBrandEnum.VISA,
    lastFourDigits: "4242",
    ...overrides
  };

  return new PaymentMethod(paymentMethodData).save();
}

async function buildTopUpAutomation(overrides: any = {}): Promise<TopUpAutomationDocument> {
  let owner: mongoose.Types.ObjectId | undefined;
  let mandate: mongoose.Types.ObjectId | undefined;
  let portfolio: mongoose.Types.ObjectId | undefined;
  let bankAccount: mongoose.Types.ObjectId | undefined;

  if (!overrides.owner) {
    const user = await buildUser();
    owner = user.id;
    bankAccount = user.bankAccounts[0].id;
  } else {
    bankAccount = (await BankAccount.find({ owner: overrides.owner }))[0]._id;
  }

  if (!overrides.portfolio) {
    portfolio = (await buildPortfolio({ owner: overrides.owner || owner })).id;
  }

  if (!overrides.mandate) {
    mandate = (
      await buildMandate({
        owner: overrides.owner || owner,
        bankAccount
      })
    ).id;
  }

  const automationData: AutomationDTOInterface = {
    owner: owner,
    portfolio: portfolio,
    mandate: mandate,
    category: "TopUpAutomation",
    frequency: "monthly",
    dayOfMonth: faker.number.int({ min: 1, max: 28 }),
    consideration: {
      amount: faker.number.int({ min: 1000, max: 5000 }),
      currency: "GBP"
    },
    ...overrides
  };

  return new TopUpAutomation(automationData).save();
}

async function buildSavingsTopUpAutomation(overrides: any = {}): Promise<SavingsTopUpAutomationDocument> {
  let owner: mongoose.Types.ObjectId | undefined;
  let mandate: mongoose.Types.ObjectId | undefined;
  let portfolio: mongoose.Types.ObjectId | undefined;
  let bankAccount: mongoose.Types.ObjectId | undefined;

  if (!overrides.owner) {
    const user = await buildUser();
    owner = user.id;
    bankAccount = user.bankAccounts[0].id;
  } else {
    bankAccount = (await BankAccount.find({ owner: overrides.owner }))[0]._id;
  }

  if (!overrides.portfolio) {
    portfolio = (await buildPortfolio({ owner: overrides.owner || owner })).id;
  }

  if (!overrides.mandate) {
    mandate = (
      await buildMandate({
        owner: overrides.owner || owner,
        bankAccount
      })
    ).id;
  }

  const automationData: SavingsTopUpAutomationDTOInterface = {
    owner: owner,
    portfolio: portfolio,
    mandate: mandate,
    category: "SavingsTopUpAutomation",
    frequency: "monthly",
    dayOfMonth: faker.number.int({ min: 1, max: 28 }),
    savingsProduct: "mmf_dist_gbp",
    consideration: {
      amount: faker.number.int({ min: 1000, max: 5000 }),
      currency: "GBP"
    },
    ...overrides
  };

  return new SavingsTopUpAutomation(automationData).save();
}

async function buildRebalanceAutomation(overrides: any = {}): Promise<RebalanceAutomationDocument> {
  let owner: mongoose.Types.ObjectId | undefined;
  let portfolio: mongoose.Types.ObjectId | undefined;
  if (!overrides.owner) {
    const user = await buildUser();
    owner = user.id;
  }

  if (!overrides.portfolio) {
    portfolio = (await buildPortfolio({ owner: overrides.owner || owner })).id;
  }

  const automationData: AutomationDTOInterface = {
    owner: owner,
    portfolio: portfolio,
    category: "RebalanceAutomation",
    frequency: "monthly",
    ...overrides
  };

  return new RebalanceAutomation(automationData).save();
}

async function buildReferralCode(overrides: Partial<ReferralCodeInterface> = {}): Promise<ReferralCodeDocument> {
  let owner: mongoose.Types.ObjectId | undefined = overrides.owner as mongoose.Types.ObjectId;
  if (!overrides.owner) {
    const user = await buildUser();
    owner = user.id;
  }

  const referralCodeData: ReferralCodeDTOInterface = {
    active: true,
    code: nanoidShort(),
    lifetime: LifetimeEnum.EXPIRING,
    ...overrides,
    owner: owner as mongoose.Types.ObjectId
  };
  return new ReferralCode(referralCodeData).save();
}

async function buildWallet(overrides: Partial<WalletDTOInterface> = {}): Promise<WalletDocument> {
  let owner: mongoose.Types.ObjectId | undefined = overrides.owner as mongoose.Types.ObjectId;
  if (!overrides.owner) {
    const user = await buildUser();
    owner = user.id;
  }

  const walletData: WalletDTOInterface = {
    activeProviders: [ProviderEnum.DEVENGO],
    ...overrides,
    owner: owner as mongoose.Types.ObjectId
  };

  return new Wallet(walletData).save();
}

async function buildWealthyhoodDividendTransaction(
  overrides: Partial<WealthyhoodDividendTransactionDTOInterface> = {}
): Promise<WealthyhoodDividendTransactionDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }
  let portfolio: PortfolioDocument | undefined;
  if (!overrides.portfolio) {
    portfolio = await buildPortfolio();
  }

  const data: WealthyhoodDividendTransactionDTOInterface = {
    owner: owner?.id,
    portfolio: portfolio?.id,
    consideration: {
      amount: faker.number.int({ min: 0, max: 100 }),
      currency: "GBP"
    },
    dividendMonth: DateUtil.getYearAndMonth(new Date()),
    createdAt: new Date(),
    price: "paid_low_monthly",
    deposit: { activeProviders: [ProviderEnum.WEALTHKERNEL] },
    ...overrides
  };

  return new WealthyhoodDividendTransaction(data).save();
}

async function buildWithdrawalCashTransaction(
  overrides: Partial<WithdrawalCashTransactionInterface> = {},
  createPortfolio = true
): Promise<WithdrawalCashTransactionDocument> {
  let withdrawalDTO = {};
  if (createPortfolio) {
    const portfolio = (await buildPortfolio({
      providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
    })) as PortfolioDocument;
    withdrawalDTO = {
      owner: portfolio.owner,
      portfolio: portfolio.id,
      providers: { wealthkernel: { id: portfolio.providers?.wealthkernel?.id, status: "Pending" } }
    };
  }

  return new WithdrawalCashTransaction({
    consideration: {
      amount: faker.number.int({ min: 0, max: 100 }),
      currency: "GBP"
    },
    bankReference: faker.string.uuid(),
    createdAt: new Date(),
    ...withdrawalDTO,
    ...overrides
  }).save();
}

async function buildKycOperation(
  overrides: Partial<KycOperationDTOInterface> = {}
): Promise<KycOperationDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }

  const data: KycOperationDTOInterface = {
    owner: owner?.id,
    activeProviders: [ProviderEnum.SUMSUB],
    providers: {
      sumsub: {
        id: faker.string.uuid(),
        submittedAt: new Date(Date.now())
      }
    },
    ...overrides
  };

  return new KycOperation(data).save();
}

async function buildRiskAssessment(
  overrides: Partial<RiskAssessmentDTOInterface> = {}
): Promise<RiskAssessmentDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }

  const data: RiskAssessmentDTOInterface = {
    owner: owner?.id,
    nationality: {
      value: "GB",
      score: 0
    },
    sourcesOfFunds: {
      value: [SourceOfFundsEnum.LinkedUKBankAccount],
      score: 0
    },
    sourcesOfWealth: {
      value: ["Salary"],
      score: 0
    },
    employmentStatus: {
      value: "FullTime",
      score: 0
    },
    volumeOfTransactions: {
      value: 7000,
      score: 5
    },
    amlScreening: {
      value: AmlScreeningResultEnum.NoHit,
      score: 0
    },
    totalScore: 5,
    createdAt: new Date(Date.now()),
    ...overrides
  };

  return new RiskAssessment(data).save();
}

async function buildSavingsTopup(
  overrides: Partial<SavingsTopupTransactionDTOInterface> = {},
  orderOverrides: Partial<OrderDTOInterface> = {},
  withOrder = true
): Promise<SavingsTopupTransactionDocument> {
  const data: SavingsTopupTransactionDTOInterface = {
    owner: new User(buildUserDTO()).id,
    portfolio: new Portfolio(buildPortfolioDTO()),
    status: "Pending",
    savingsProduct: "mmf_dist_gbp",
    consideration: {
      currency: "GBP",
      amount: faker.number.int({ min: 1 })
    },
    createdAt: new Date(Date.now()),
    ...overrides
  };

  const savingsTopup = await new SavingsTopupTransaction(data).save();
  if (withOrder) {
    await buildOrder({
      transaction: savingsTopup.id,
      isin: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"].isin,
      side: "Buy",
      consideration: {
        originalAmount: savingsTopup.consideration.amount,
        amountSubmitted: savingsTopup.consideration.amount,
        amount: savingsTopup.consideration.amount,
        currency: "GBP"
      },
      createdAt: new Date(Date.now()),
      updatedAt: new Date(Date.now()),
      ...orderOverrides
    });
    await savingsTopup.populate("orders");
  }

  return savingsTopup;
}

async function buildSavingsWithdrawal(
  overrides: Partial<SavingsWithdrawalTransactionDTOInterface> = {},
  orderOverrides: Partial<OrderDTOInterface> = {},
  withOrder = true
): Promise<SavingsWithdrawalTransactionDocument> {
  const data: SavingsWithdrawalTransactionDTOInterface = {
    owner: new User(buildUserDTO()).id,
    portfolio: new Portfolio(buildPortfolioDTO()),
    status: "Pending",
    savingsProduct: "mmf_dist_gbp",
    consideration: {
      currency: "GBP",
      amount: faker.number.int({ min: 1 })
    },
    createdAt: new Date(Date.now()),
    ...overrides
  };

  const savingsWithdrawal = await new SavingsWithdrawalTransaction(data).save();
  if (withOrder) {
    await buildOrder({
      transaction: savingsWithdrawal.id,
      isin: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"].isin,
      side: "Sell",
      consideration: {
        originalAmount: savingsWithdrawal.consideration.amount,
        amountSubmitted: savingsWithdrawal.consideration.amount,
        amount: savingsWithdrawal.consideration.amount,
        currency: "GBP"
      },
      createdAt: new Date(Date.now()),
      updatedAt: new Date(Date.now()),
      ...orderOverrides
    });
    await savingsWithdrawal.populate("orders");
  }

  return savingsWithdrawal;
}

async function buildSavingsDividend(
  overrides: Partial<SavingsDividendTransactionDTOInterface> = {}
): Promise<SavingsDividendTransactionDocument> {
  const data: SavingsDividendTransactionDTOInterface = {
    owner: new User(buildUserDTO()).id,
    portfolio: new Portfolio(buildPortfolioDTO()),
    savingsProduct: "mmf_dist_gbp",
    status: "Pending",
    originalDividendAmount: faker.number.int({ min: 1, max: 5000 }),
    consideration: {
      currency: "GBP",
      amount: faker.number.int({ min: 1, max: 5000 })
    },
    fees: {
      fx: {
        amount: 0,
        currency: "GBP"
      },
      commission: {
        amount: faker.number.int({ min: 1, max: 50 }),
        currency: "GBP"
      }
    },
    createdAt: new Date(Date.now()),
    dividendMonth: DateUtil.getYearAndMonth(DateUtil.getFirstDayOfLastMonth()),
    activeProviders: [ProviderEnum.WEALTHKERNEL],
    ...overrides
  };

  return new SavingsDividendTransaction(data).save();
}

async function buildDailyPortfolioSavingsTicker(
  overrides: Partial<DailyPortfolioSavingsTickerDocument> = {}
): Promise<DailyPortfolioSavingsTickerDocument> {
  const portfolio = overrides.portfolio || new Portfolio(buildPortfolioDTO());
  const savingsProduct = overrides.savingsProduct || (await buildSavingsProduct(false));

  return new DailyPortfolioSavingsTicker({
    portfolio: portfolio,
    planPrice: "free_monthly",
    savingsProduct,
    holdingAmount: 10000,
    planFee: 0.006,
    dailyAccrual: 1,
    ...overrides
  }).save();
}

async function buildDailySavingsProductTicker({
  savingsProduct = new mongoose.Types.ObjectId(),
  ...overrides
}: Partial<DailySavingsProductTickerDocument> = {}): Promise<DailySavingsProductTickerDocument> {
  // The one-day yield cannot be less than lowest wealthyhood fee which is 0.60%
  const minOneDayYield = 0.6;

  // Set a random daily distribution factor that will not result in a one-day yield less than 1%
  const minDailyDistributionFactor = minOneDayYield + 0.01 / 365;
  const dailyDistributionFactor = faker.number.float({
    min: minDailyDistributionFactor,
    max: 1,
    multipleOf: 0.000000001
  });
  const oneDayYield = dailyDistributionFactor * 365;

  return new DailySavingsProductTicker({
    dailyDistributionFactor: dailyDistributionFactor,
    oneDayYield: oneDayYield,
    fixingDate: new Date(),
    date: new Date(),
    savingsProduct: savingsProduct,
    ...overrides
  }).save();
}

async function buildSavingsProduct(
  createTicker = true,
  {
    commonId = faker.helpers.arrayElement(SavingsProductArray),
    ...overrides
  }: Partial<SavingsProductInterface> = {},
  dailyTickerOverrides: Partial<DailySavingsProductTickerDocument> = {}
): Promise<SavingsProductDocument> {
  const savingsProductDocument = new SavingsProduct({
    commonId: commonId,
    buyLine: { active: true },
    sellLine: { active: true },
    ...overrides
  });
  const savingsProduct = await savingsProductDocument.save({ validateBeforeSave: false });
  if (createTicker) {
    await buildDailySavingsProductTicker({
      savingsProduct: savingsProductDocument.id,
      ...dailyTickerOverrides
    });
    await savingsProduct.populate("currentTicker");
  }

  return savingsProduct;
}

function buildValidObjectId(): string {
  return faker.string.sample(MONGOOSE_OBJECT_ID_LENGTH);
}

async function buildAssetNews(overrides: Partial<AssetNewsDTOInterface> = {}): Promise<AssetNewsDocument> {
  const data: AssetNewsDTOInterface = {
    providers: {
      stockNews: {
        id: faker.string.uuid()
      }
    },
    date: new Date(Date.now()),
    imageUrl: "https://someImageurl.com",
    investmentProducts: [],
    newsUrl: "https://someurl.com",
    sentiment: AssetNewsSentimentEnum.Positive,
    source: "The Motley Fool",
    text: "Text about stock news",
    tickers: ["ABBV", "SNAP"],
    title: "Title about stock news",
    topics: ["Interest Rates"],
    type: "Article",
    ...overrides
  };

  return new AssetNews(data).save();
}

async function buildAppRating(overrides: Partial<AppRatingDTOInterface> = {}): Promise<AppRatingDocument> {
  const data: AppRatingDTOInterface = {
    owner: new User(buildUserDTO()).id,
    ...overrides
  };

  return new AppRating(data).save();
}

async function buildTransactionMonitor(
  overrides: Partial<TransactionMonitorDTOInterface> = {}
): Promise<TransactionMonitorDocument> {
  const data: TransactionMonitorDTOInterface = {
    owner: new User(buildUserDTO()).id,
    type: MonitorType.AGGREGATE_AMOUNT_FOR_LOW_RISK_USER,
    ...overrides
  };

  return new TransactionMonitor(data).save();
}

async function buildRequisition(overrides: Partial<RequisitionDTOInterface> = {}): Promise<RequisitionDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }

  const data: RequisitionDTOInterface = {
    owner: owner?.id,
    reference: faker.string.uuid(),
    providers: {
      gocardlessData: {
        id: faker.string.uuid()
      }
    },
    ...overrides
  };

  return new Requisition(data).save();
}

async function buildSundownDigest(
  overrides: Partial<SundownDigestDTOInterface> = {}
): Promise<SundownDigestDocument> {
  const data: SundownDigestDTOInterface = {
    date: new Date(Date.now()),
    content: faker.lorem.sentence(),
    ...overrides
  };

  return new SundownDigest(data).save();
}

async function buildIndexPrice(overrides: Partial<IndexPriceDTOInterface> = {}): Promise<IndexPriceDocument> {
  const data: IndexPriceDTOInterface = {
    date: new Date(Date.now()),
    index: faker.helpers.arrayElement(indexesConfig.IndexArrayConst),
    price: faker.number.int({ min: 1000, max: 8000 }),
    dailyReturnPercentage: faker.number.int({ min: -1, max: 1 }),
    ...overrides
  };

  return new IndexPrice(data).save();
}

async function buildNotification(
  overrides: Partial<NotificationDTOInterface> = {}
): Promise<NotificationDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }

  const data: NotificationDTOInterface = {
    owner: owner?.id,
    method: "app",
    status: "Pending",
    providers: {
      onesignal: {
        notificationId: LearningNotificationEventEnum.QUICK_TAKE_CREATED,
        properties: new Map(
          Object.entries({
            title: "Great quick take!"
          })
        )
      }
    },
    ...overrides
  };

  return new Notification(data).save();
}

async function buildNotificationSettings(
  overrides: Partial<NotificationSettingsDTOInterface> = {}
): Promise<NotificationSettingsDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }

  const data: NotificationSettingsDTOInterface = {
    owner: owner?.id,
    app: {
      deviceNotificationsEnabled: true,
      settings: new Map(
        Object.entries({
          [AppNotificationSettingEnum.TRANSACTIONAL]: true,
          [AppNotificationSettingEnum.LEARNING_GUIDE]: true,
          [AppNotificationSettingEnum.ANALYST_INSIGHT]: true,
          [AppNotificationSettingEnum.DAILY_RECAP]: true,
          [AppNotificationSettingEnum.QUICK_TAKE]: true,
          [AppNotificationSettingEnum.WEEKLY_REVIEW]: true,
          [AppNotificationSettingEnum.PROMOTIONAL]: true
        })
      ) as AppNotificationSettings
    },
    email: {
      settings: new Map(
        Object.entries({
          [EmailNotificationSettingEnum.TRANSACTIONAL]: true,
          [EmailNotificationSettingEnum.PROMOTIONAL]: true,
          [EmailNotificationSettingEnum.WEALTHYBITES]: true
        })
      ) as EmailNotificationSettings
    },
    ...overrides
  };

  return new NotificationSettings(data).save();
}

async function buildPayout(overrides: Partial<PayoutDTOInterface> = {}): Promise<PayoutDocument> {
  const data: PayoutDTOInterface = {
    reference: faker.string.uuid(),
    status: "Pending",
    activeProviders: [ProviderEnum.GOCARDLESS, ProviderEnum.DEVENGO],
    providers: {
      gocardless: {
        id: faker.string.uuid()
      }
    },
    ...overrides
  };

  return new Payout(data).save();
}

async function buildContentEntry(
  overrides: Partial<ContentEntryDTOInterface> = {}
): Promise<ContentEntryDocument> {
  const data: ContentEntryDTOInterface = {
    contentType: ContentEntryContentTypeEnum.ANALYSIS,
    category: ContentEntryCategoryEnum.ANALYST_INSIGHTS,
    activeProviders: [ProviderEnum.FINIMIZE, ProviderEnum.CONTENTFUL],
    title: "This Company's Shares Are Up Nearly 200% This Year, Thanks To Bitcoin",
    providers: {
      finimize: {
        id: faker.string.uuid(),
        contentType: FinimizeContentTypeEnum.INSIGHT,
        publishedAt: new Date()
      },
      contentful: {
        id: faker.string.uuid(),
        spaceId: faker.string.uuid(),
        environmentId: faker.string.uuid()
      }
    },
    shouldNotifyUsers: false,
    ...overrides
  };

  return new ContentEntry(data).save();
}

async function buildLearnNews(overrides: Partial<LearnNewsDTOInterface> = {}): Promise<LearnNewsDocument> {
  const data = {
    hash: faker.string.uuid(),
    imageUrl: faker.internet.url(),
    title: "Sample Learn News Title",
    htmlContent: "Sample content for learn news article.",
    date: new Date(),
    readingTime: "1 min",
    ...overrides
  };

  return new LearnNews(data).save();
}

async function buildCreditTicket(
  overrides: Partial<CreditTicketDTOInterface> = {}
): Promise<CreditTicketDocument> {
  let owner: UserDocument | undefined;
  if (!overrides.owner) {
    owner = await buildUser();
  }
  let portfolio: PortfolioDocument | undefined;
  if (!overrides.portfolio) {
    portfolio = await buildPortfolio({ owner: owner?.id });
  }

  const data: CreditTicketDTOInterface = {
    owner: owner?.id,
    portfolio: portfolio?.id,
    consideration: {
      currency: "EUR",
      amount: faker.number.int()
    },
    deposit: {
      activeProviders: [ProviderEnum.WEALTHKERNEL]
    },
    status: "Pending",
    ...overrides
  };

  return new CreditTicket(data).save();
}

async function buildInvoiceReferenceNumber(
  overrides: Partial<InvoiceReferenceNumberInterface> = {}
): Promise<InvoiceReferenceNumberDocument> {
  const data: Partial<InvoiceReferenceNumberInterface> = {
    linkedDocumentId: new mongoose.Types.ObjectId(),
    sourceDocumentType: "Order",
    createdAt: new Date(),
    ...overrides
  };

  return new InvoiceReferenceNumber(data).save();
}

export {
  buildAddress,
  buildAccount,
  buildAssetTransactionDTO,
  buildAssetTransaction,
  buildBankAccount,
  buildDailyPortfolioTicker,
  buildHoldingDTO,
  buildDailyPortfolioTickersBulk,
  buildIntraDayAssetTicker,
  buildIntraDayPortfolioTicker,
  buildIntraDayPortfolioTickersBulk,
  buildInvestmentProduct,
  buildPortfolioDTO,
  buildPortfolio,
  buildUserDTO,
  buildOrder,
  buildParticipant,
  buildReward,
  buildUser,
  buildDepositCashTransaction,
  buildWithdrawalCashTransaction,
  buildValidObjectId,
  buildRebalanceTransaction,
  buildDividendTransaction,
  buildAssetDividendTransaction,
  buildChargeTransaction,
  buildRevertRewardTransaction,
  buildSubscription,
  buildUserDataRequest,
  buildGift,
  buildMandate,
  buildDailySummarySnapshot,
  buildTopUpAutomation,
  buildSavingsTopUpAutomation,
  buildRebalanceAutomation,
  buildRewardInvitation,
  buildReferralCode,
  buildCashbackTransaction,
  buildWealthyhoodDividendTransaction,
  buildPaymentMethod,
  buildKycOperation,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildSavingsDividend,
  buildSavingsProduct,
  buildDailySavingsProductTicker,
  buildDailyPortfolioSavingsTicker,
  buildRiskAssessment,
  buildAssetNews,
  buildWallet,
  buildTransactionMonitor,
  buildRequisition,
  buildPayout,
  buildAppRating,
  buildStockSplitCorporateEvent,
  buildStockSplitTransaction,
  buildAssetIsinChangeCorporateEvent,
  buildNotificationSettings,
  buildNotification,
  buildContentEntry,
  buildSundownDigest,
  buildIndexPrice,
  buildCreditTicket,
  buildLearnNews,
  buildInvoiceReferenceNumber
};
