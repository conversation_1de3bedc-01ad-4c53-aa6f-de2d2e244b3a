import Decimal from "decimal.js";
import { addBreadcrumb, captureException } from "@sentry/node";
import { PartialRecord } from "utils";
import { ForeignCurrencyRatesType } from "currencies";
import { UserDocument, UserPopulationFieldsEnum } from "../models/User";
import {
  Order,
  OrderDocument,
  OrderDTOInterface,
  OrderInterface,
  OrderPopulationFieldsEnum,
  OrderStatusType,
  OrderSubmissionIntentEnum,
  UnitPriceType
} from "../models/Order";
import {
  AssetTransactionDocument,
  ChargeTransactionDocument,
  FeesType,
  RebalanceTransactionDocument,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument,
  Transaction,
  TransactionDocument,
  TransactionPopulationFieldsEnum
} from "../models/Transaction";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import {
  OrderType,
  WealthkernelOrderStatusType,
  WealthkernelOrderStatusWithSettledType
} from "../external-services/wealthkernelService";
import logger from "../external-services/loggerService";
import DateUtil from "../utils/dateUtil";
import { AnalyticsByDateType, AnalyticsResponse, UnsubmittedOrderAnalyticsResponse } from "apiResponse";
import InvestmentProductService from "./investmentProductService";
import PortfolioUtil from "../utils/portfolioUtil";
import {
  currenciesConfig,
  entitiesConfig,
  fees,
  investmentsConfig,
  investmentUniverseConfig,
  plansConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import * as InvestmentUniverseUtil from "../utils/investmentUniverseUtil";
import {
  HoldingsType,
  InitialHoldingsAllocationType,
  PortfolioDocument,
  PortfolioPopulationFieldsEnum
} from "../models/Portfolio";
import { Reward, RewardDocument } from "../models/Reward";
import PortfolioService, { PendingOrderType } from "./portfolioService";
import mongoose, { QueryOptions } from "mongoose";
import { OrdersFilter } from "filters";
import DbUtil from "../utils/dbUtil";
import {
  calculateBrokerFxFee,
  calculateDisplayFxFee,
  calculateFXRateWithSpread,
  getTotalFeeAmount,
  getZeroFees
} from "../utils/feesUtil";
import {
  SAVINGS_PRODUCT_MINIMUM_WK_ORDER_AMOUNT,
  TransactionService,
  TransactionWithOrders
} from "./transactionService";
import UserService from "./userService";
import { SubscriptionDocument } from "../models/Subscription";
import { OrderDataType } from "./brokerageService";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import { RedisClientService } from "../loaders/redis";
import { InvestmentProductsDictType } from "investmentProducts";
import SavingsProductService from "./savingsProductService";
import { BadRequestError, ForbiddenError } from "../models/ApiErrors";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import CurrencyUtil from "../utils/currencyUtil";
import { MainCurrencyToWealthkernelCurrency } from "../configs/currenciesConfig";
import { DURATIONS_MAP, TenorEnum } from "../configs/durationConfig";
import { ProviderEnum } from "../configs/providersConfig";
import SubmissionWindowUtil from "../utils/submissionWindowUtil";
import { FX_FEE_SPREADS_WH } from "../configs/fxSpreadsConfig";
import { FXSpreadSide } from "../types/fees";

const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;
const { ASSET_CONFIG } = investmentUniverseConfig;
const { MIN_ALLOWED_REBALANCE_ORDER_AMOUNT, MIN_ALLOWED_ASSET_QUANTITY } = investmentsConfig;
const { COMMISSION_RATES, MINIMUM_COMMISSION_FEE, EXECUTION_SPREAD_RATES, REAL_TIME_ETF_EXECUTION_FEES } = fees;

const REALTIME_ORDER_SUBMISSION_BATCH_SIZE = 5;

type AnalyticType =
  | "buyOrders"
  | "buyConsideration"
  | "sellOrders"
  | "sellConsideration"
  | "allMatched"
  | "allRejected"
  | "allInTerminalState";

export type OrderActivityItemType = {
  side: "Buy" | "Sell";
  quantity: number;
  timestamp: number;
};

// When creating *sell* orders, we have three ways of filtering orders before creating them.
// 1. `filter-and-discard-quantity-only` is used when we don't want to do any filtering on the amount of the orders, i.e. orders of all
// amounts >= £0.01 and quantity >= MIN_ALLOWED_ASSET_QUANTITY are allowed to be created. Orders below those values
// are discarded.
// 2. `filter-and-distribute` is used when we want to do filtering on both amounts & quantity
// (MIN_ALLOWED_ASSET_ORDER_AMOUNT and MIN_ALLOWED_ASSET_QUANTITY respectively), but instead of discarding the amounts.
// that are below these threshold, we re-distribute them to other orders in the transaction.
// 3. `filter-and-discard` is used when we want to filter and *discard* all orders that are below certain thresholds
// (MIN_ALLOWED_ASSET_INVESTMENT and MIN_ALLOWED_ASSET_QUANTITY respectively)
export enum SellOrderFilteringMethodEnum {
  FILTER_AND_DISCARD_QUANTITY_ONLY = "filter-and-discard-quantity-only",
  FILTER_AND_DISCARD = "filter-and-discard",
  FILTER_AND_DISTRIBUTE = "filter-and-distribute"
}

/**
 * TODO Revisit this when we:
 * Exchange rate for savings products.
 * - Support EU portfolios
 * or
 * - Add more Savings Products with diffrent base currency
 */
const SAVINGS_EXCHANGE_RATE = 1;

class OrderService {
  public static getMatchedOrdersForTransactions(transactions: {
    assetTransactions: AssetTransactionDocument[];
    rebalanceTransactions: RebalanceTransactionDocument[];
    chargeTransactions: ChargeTransactionDocument[];
  }): OrderDocument[] {
    const { assetTransactions, rebalanceTransactions, chargeTransactions } = transactions;

    const orders = assetTransactions
      .flatMap(({ orders }) => orders)
      .concat(rebalanceTransactions.flatMap(({ orders }) => orders))
      .concat(chargeTransactions.flatMap(({ orders }) => orders))
      .filter((order) => order.isMatched);

    return orders;
  }

  /**
   * @description Checks if a wealthkernel order status is terminal.
   * Terminal status for successful orders: Matched.
   * Terminal status for failed orders: Rejected & Cancelled.
   * @param orderStatus
   */
  public static isOrderWkStatusTerminal(orderStatus: WealthkernelOrderStatusType): boolean {
    return ["Matched", "Rejected", "Cancelled"].includes(orderStatus);
  }

  /**
   * Syncs **asset transaction** orders using their WK entity.
   * @param orderId
   * @param newOrderStatus
   */
  public static async syncOrderByWealthkernelId(
    orderId: string,
    newOrderStatus: WealthkernelOrderStatusWithSettledType
  ): Promise<void> {
    if (["Pending", "Open", "Cancelling"].includes(newOrderStatus)) return;

    await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
      const order = await Order.findById(orderId).populate("transaction").session(session);

      if (!OrderService._canTransitionFromStatusToWkStatus(order.status, newOrderStatus)) {
        logger.warn(
          `Cannot update WK order as the transition from ${order.status} to ${newOrderStatus} is not valid`,
          {
            module: "OrderService",
            method: "syncOrderByWealthkernelId",
            data: {
              order
            }
          }
        );

        throw new ForbiddenError(
          `Cannot update order ${order.id} as the transition from ${order.status} to ${newOrderStatus} is not valid`
        );
      }

      logger.info(`Syncing order ${order.id}`, {
        module: "OrderService",
        method: "syncOrderByWealthkernelId",
        data: {
          fromStatus: order.status,
          newOrderStatus
        }
      });

      const transaction = order.transaction as TransactionWithOrders;
      const user = await UserService.getUser(transaction.owner.toString(), {
        addresses: false,
        portfolios: true,
        subscription: true
      });

      const wkOrderData = await ProviderService.getBrokerageService(user.companyEntity).retrieveOrder(
        order.providers?.wealthkernel?.id
      );
      if (!wkOrderData) {
        throw new Error(
          `Order ${order.id} of transaction ${transaction.id} could not be retrieved from Wealthkernel.`
        );
      }

      if (newOrderStatus === "Matched") {
        const fills = wkOrderData?.fills?.filter((fill) => fill.status == "Matched");
        if (!fills || fills?.length === 0) {
          throw new Error(
            `Order ${order.id} of transaction ${transaction.id} has no matched fills but its status is Matched.`
          );
        }

        let orderData: Partial<OrderDTOInterface> = {
          status: "Matched",
          consideration: {
            amount: Decimal.mul(OrderService.calculateMatchedOrderAmount(wkOrderData), 100).toNumber(),
            currency: fills[0].consideration.currency,
            originalAmount: order.consideration?.originalAmount,
            amountSubmitted: order.consideration?.amountSubmitted
          },
          quantity: OrderService.calculateMatchedOrderQuantity(wkOrderData),
          unitPrice: OrderService.calculateMatchedOrderUnitPrice(wkOrderData),
          marketSettledAt: OrderService.calculateMatchedOrderMarketSettledAt(wkOrderData),
          filledAt: fills[0].filledAt,
          updatedAt: new Date()
        };

        if (order.isInvestmentOrder) {
          const investmentProduct = await InvestmentProductService.getInvestmentProductByIsin(order.isin, false);
          const plan = (user.subscription as SubscriptionDocument).plan;

          // We only store the exchange rate if the order is foreign currency traded.
          if (CurrencyUtil.isForeignCurrency(user.currency, investmentProduct.tradedCurrency)) {
            // Calculate our exchange rate with spread
            const exchangeRate = await OrderService.calculateMatchedOrderExchangeRateWithSpread(wkOrderData, plan);
            orderData = { ...orderData, exchangeRate };
          }

          // If the order is a non-charge sell order, we calculate its fees and apply it to the order consideration.
          // We don't do that for buy orders, as for those, the fees are calculated & stored when the orders are first created.
          if (
            !["ChargeTransaction", "RevertRewardTransaction"].includes(transaction.category) &&
            order.side === "Sell"
          ) {
            const fees = OrderService.calculateFeesForSingleOrder(
              plan,
              { ...orderData, isin: order.isin, submissionIntent: order.submissionIntent } as Omit<
                OrderDTOInterface,
                "transaction"
              >,
              { investmentProduct, userCurrency: user.currency }
            );
            const totalFeeAmount = getTotalFeeAmount(fees);

            orderData = {
              ...orderData,
              fees,
              consideration: {
                amount: Decimal.sub(orderData.consideration.amount, Decimal.mul(totalFeeAmount, 100)).toNumber(),
                originalAmount: orderData.consideration.amount,
                currency: user.currency
              }
            };
          }

          // Raw broker FX rate from WealthKernel
          const brokerFxRate = fills[0].exchangeRate || 1;
          // Base exchange rate from WealthKernel
          const baseExchangeRate = fills[0].baseExchangeRate;
          // Calculate broker FX fee and display FX fee based on consideration amount and plan
          const brokerFxFee = calculateBrokerFxFee({
            considerationAmount: OrderService.calculateMatchedOrderAmount(wkOrderData),
            plan,
            brokerFxRate
          });
          const displayFxFee = calculateDisplayFxFee({
            considerationAmount: OrderService.calculateMatchedOrderAmount(wkOrderData),
            plan
          });

          const updatedOrder = await Order.findByIdAndUpdate(
            {
              _id: order.id,
              "providers.wealthkernel.status": order.providers.wealthkernel.status
            },
            {
              ...orderData,
              displayFxFee,
              "providers.wealthkernel.brokerFxRate": brokerFxRate,
              "providers.wealthkernel.baseExchangeRate": baseExchangeRate,
              "providers.wealthkernel.accountingBrokerFxFee": brokerFxFee,
              "providers.wealthkernel.status": "Matched"
            },
            { upsert: false, runValidators: true, setDefaultsOnInsert: false, new: true, session }
          );

          const portfolioId = user.portfolios[0].id;
          if (["AssetTransaction", "ChargeTransaction", "RebalanceTransaction"].includes(transaction.category)) {
            // We want to clear the cache to avoid displaying the change in holdings as increase in returns.
            // MWRR & value will be cached when portfolio returns are requested again through dashboard (or at cron).
            await Promise.all([
              RedisClientService.Instance.del(`portfolios:mwrr:${portfolioId}`),
              RedisClientService.Instance.del(`portfolios:value_at_mwrr:${portfolioId}`),
              RedisClientService.Instance.del(`portfolios:up_by:${portfolioId}`),
              RedisClientService.Instance.del(`portfolios:value_at_up_by:${portfolioId}`)
            ]);
          }

          if (transaction.category === "AssetTransaction") {
            await PortfolioService.updatePortfolioHoldings(portfolioId, [updatedOrder], { session });

            if ((transaction as AssetTransactionDocument).pendingGift) {
              await PortfolioService.updatePortfolioGiftedHoldings(
                portfolioId,
                [updatedOrder],
                (transaction as AssetTransactionDocument).pendingGift.toString(),
                { session }
              );
            }

            if (updatedOrder.side === "Sell") {
              await PortfolioService.updateCashAvailability(
                portfolioId,
                user.currency,
                Decimal.div(updatedOrder.consideration.amount, 100).toNumber(),
                { session, available: true, settled: false }
              );
            }

            await UserService.convertUser(user, "completed", { session });

            // Syncing of the asset transaction has to remain the last operation in this session, as it involves
            // sending a notification to the user that we would not be able to roll back if a subsequent operation
            // was to fail.
            await TransactionService.syncAssetTransaction(
              transaction as AssetTransactionDocument,
              { [updatedOrder.isin]: investmentProduct },
              { session }
            );
          }
        } else if (order.isSavingsOrder) {
          await Order.findByIdAndUpdate(
            order.id,
            {
              ...orderData,
              exchangeRate: SAVINGS_EXCHANGE_RATE,
              "providers.wealthkernel.status": "Matched"
            },
            { upsert: false, runValidators: true, setDefaultsOnInsert: false, new: true, session }
          );
        }
      } else if (newOrderStatus === "Settled") {
        await Order.findByIdAndUpdate(order.id, { status: newOrderStatus }, { session });

        if (order.isInvestmentOrder && transaction.category === "AssetTransaction" && order.side === "Sell") {
          await PortfolioService.updateCashAvailability(
            user.portfolios[0].id,
            user.currency,
            Decimal.div(order.consideration.amount, 100).toNumber(),
            { session, available: false, settled: true }
          );
        }
      } else if (newOrderStatus === "Rejected") {
        logger.error(`Order ${order._id} of transaction ${order.transaction} was rejected!`, {
          module: "OrderService",
          method: "syncOrderByWealthkernelId"
        });

        await Order.findByIdAndUpdate(
          order.id,
          {
            rejectionReason: wkOrderData.reason,
            "providers.wealthkernel.status": newOrderStatus,
            updatedAt: new Date()
          },
          { upsert: false, runValidators: true, setDefaultsOnInsert: false, new: true, session }
        );

        eventEmitter.emit(events.order.orderRejection.eventId, user, {
          asset: order.commonId,
          amount:
            order.side === "Buy" ? Decimal.div(order.consideration?.originalAmount, 100).toNumber() : undefined,
          currency: order.consideration?.currency ?? user.currency,
          side: order.side,
          quantity: order.quantity,
          rejectionReason: wkOrderData.reason
        });
      }
    });
  }

  /**
   * @description Calculates the quantity of a matched order, by aggregating the
   * individual fills of the order.
   * @param order a wealthkernel order object
   * @returns the matched quantity
   */
  public static calculateMatchedOrderQuantity(order: OrderType): number {
    if (!order?.fills) {
      return 0;
    }

    const fills = order.fills.filter((fill) => fill.status == "Matched");
    if (fills.length > 0) {
      // means order was filled
      return fills
        .map(({ quantity }) => new Decimal(quantity))
        .reduce((sum, quantity) => sum.plus(quantity), new Decimal(0))
        .toNumber();
    }

    return 0;
  }

  /**
   * Calculates the unit price of a matched order, by aggregating the
   * individual fills of the order.
   * @param order a wealthkernel order object
   * @returns the unit price object
   */
  public static calculateMatchedOrderUnitPrice(order: OrderType): UnitPriceType {
    const fills = order.fills.filter((fill) => fill.status == "Matched");

    if (!fills || fills.length === 0) {
      throw new Error("Cannot calculate matched order unit price if order does not have fills!");
    } else if (new Set(fills.map((fill) => fill.price.currency)).size !== 1) {
      throw new Error(
        "Cannot calculate matched order unit price if order has been filled in more than 1 currency!"
      );
    }

    const totalQuantity = fills
      .map(({ quantity }) => new Decimal(quantity))
      .reduce((quantity1, quantity2) => quantity1.plus(quantity2), new Decimal(0))
      .toNumber();

    const averageUnitPrice = fills
      .map(({ quantity, price }) => new Decimal(quantity).mul(price.amount))
      .reduce((value1, value2) => value1.plus(value2), new Decimal(0))
      .div(totalQuantity)
      .toDecimalPlaces(2)
      .toNumber();
    const orderCurrency = fills[0].price.currency;

    return {
      amount: averageUnitPrice,
      currency: orderCurrency as currenciesConfig.MainCurrencyType
    };
  }

  /**
   * Calculates the settlement date for this order. Note this is **NOT** related to our 'Settled' status,
   * but the actual market settlement that happens at T+1 where T is the trade execution date.
   * @param order a wealthkernel order object
   *
   * If WK has > 1 fills and they have different settlement dates, we return the one furthest from today into the
   * future.
   */
  public static calculateMatchedOrderMarketSettledAt(order: OrderType): Date {
    const fills = order.fills.filter((fill) => fill.status == "Matched");

    if (!fills || fills.length === 0) {
      throw new Error("Cannot calculate matched order settlement date if order does not have fills!");
    }

    return fills.reduce(
      (final, current) =>
        DateUtil.isFutureDate(new Date(current.settlementDate), new Date(final.settlementDate)) ? current : final,
      fills[0]
    ).settlementDate;
  }

  /**
   * Calculates the matched order exchange rate WITH our spread applied. There are two ways to calculate this:
   * 1) If the order includes WK exchange rate, then we use that as the base exchange rate and we apply our FX
   * spread to that.
   * 2) If the order does not include a WK exchange rate we retrieve the latest one (cached in Redis from EOD)
   * and apply our FX spread to that.
   *
   * @param order
   * @param plan
   */
  public static async calculateMatchedOrderExchangeRateWithSpread(
    order: OrderType,
    plan: plansConfig.PlanType
  ): Promise<number> {
    const fills = order.fills.filter((fill) => fill.status == "Matched");

    if (!fills || fills.length === 0) {
      throw new Error("Cannot calculate matched order unit price if order does not have fills!");
    } else if (new Set(fills.map((fill) => fill.price.currency)).size !== 1) {
      throw new Error(
        "Cannot calculate matched order unit price if order has been filled in more than 1 currency!"
      );
    } else if (!fills[0]?.baseExchangeRate) {
      throw new Error(`Cannot calculate exchange rate for order ${order.id} that has no base exchange rate!`);
    }

    return calculateFXRateWithSpread(order.side === "Buy" ? FXSpreadSide.BUY : FXSpreadSide.SELL, plan, {
      exchangeRate: fills[0].baseExchangeRate,
      rounding: false
    });
  }

  /**
   * @description Calculates the amount of a matched order (in GBP, not cents),
   * by aggregating the individual fills of the order.
   * @param order a wealthkernel order object
   * @returns the matched amount
   */
  public static calculateMatchedOrderAmount(order: OrderType): number {
    if (!order?.fills) {
      return 0;
    }

    const fills = order.fills.filter((fill) => fill.status == "Matched");
    if (fills.length > 0) {
      // means order was filled
      return fills
        .map((fill) => new Decimal(fill.consideration.amount))
        .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
        .toNumber();
    }

    return 0;
  }

  /**
   * @description Returns analytics such as # buy orders, # sell orders,
   * total buy/sell consideration, etc. grouped by date.
   * @param monthOffset (required if specificDate is not passed)
   * @param specificDate (required if pageConfig is not passed)
   */
  public static async getAnalytics(monthOffset = 0, specificDate?: string): Promise<AnalyticsResponse> {
    const investmentProductsDict = await InvestmentProductService.getInvestmentProductsDict("isin", true, {
      listedOnly: false
    });

    // month offset to go back calculated by page number provided
    const actualMonthOffset = -(monthOffset - 1);

    // Mongo pipeline step that returns orders & reward orders in our DB grouped by date of submission.
    // The following aggregation query works either for a month or a specific date
    const groupOrdersBySubmittedAt = [
      {
        $lookup: {
          from: "transactions",
          localField: "transaction",
          foreignField: "_id",
          as: "transaction"
        }
      },
      {
        $match: {
          "transaction.category": {
            $in: ["AssetTransaction", "RebalanceTransaction"]
          }
        }
      },
      {
        $match: {
          "providers.wealthkernel.submittedAt": {
            $gte: specificDate
              ? new Date(specificDate)
              : DateUtil.getFirstDayOfMonthWithOffset(actualMonthOffset - 1),
            $lt: specificDate
              ? DateUtil.getDateAfterNdays(new Date(specificDate), 1)
              : DateUtil.getFirstDayOfMonthWithOffset(actualMonthOffset)
          }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: "%Y-%m-%d",
              date: "$providers.wealthkernel.submittedAt"
            }
          },
          orders: {
            $push: "$$ROOT"
          }
        }
      },
      {
        $unionWith: {
          coll: "rewards",
          pipeline: [
            {
              $match: {
                "order.providers.wealthkernel.submittedAt": {
                  $gte: specificDate
                    ? new Date(specificDate)
                    : DateUtil.getFirstDayOfMonthWithOffset(actualMonthOffset - 1),
                  $lt: specificDate
                    ? DateUtil.getDateAfterNdays(new Date(specificDate), 1)
                    : DateUtil.getFirstDayOfMonthWithOffset(actualMonthOffset)
                }
              }
            },
            {
              $group: {
                _id: {
                  $dateToString: {
                    format: "%Y-%m-%d",
                    date: "$order.providers.wealthkernel.submittedAt"
                  }
                },
                rewards: {
                  $push: "$$ROOT"
                }
              }
            }
          ]
        }
      },
      {
        $group: {
          _id: "$_id",
          orders: {
            $push: "$orders"
          },
          rewards: {
            $push: "$rewards"
          }
        }
      },
      {
        $project: {
          orders: {
            $arrayElemAt: ["$orders", 0]
          },
          rewards: {
            $arrayElemAt: ["$rewards", 0]
          }
        }
      }
    ];

    const ordersGroupedByDate = await Order.aggregate(groupOrdersBySubmittedAt).sort({ _id: -1 });

    const ordersByDate = ordersGroupedByDate.map(({ _id, orders, rewards }) => {
      const ordersToGetAnalyticsFor = (orders ?? []) as Partial<OrderInterface>[];
      const rewardsToGetAnalyticsFor = (rewards ?? []).map((reward: RewardDocument) => {
        return {
          consideration: reward.consideration,
          isin: ASSET_CONFIG[reward.asset].isin,
          side: "Buy",
          providers: reward.order.providers
        };
      }) as Partial<OrderInterface>[];
      const allOrders = ordersToGetAnalyticsFor.concat(rewardsToGetAnalyticsFor);

      return {
        date: _id,
        lines: Object.fromEntries(
          allOrders.map(({ isin }) => [
            investmentProductsDict[isin].commonId,
            OrderService._getAnalyticsForOrders(
              allOrders.filter((order) => order.isin == isin),
              investmentProductsDict
            )
          ])
        )
      } as AnalyticsByDateType;
    });

    return { analytics: ordersByDate };
  }

  /**
   * @description Returns analytics such as # buy orders, # sell orders,
   * total buy/sell consideration, etc. for all orders that are planned to be
   * submitted in the next run.
   */
  public static async getUnsubmittedOrderAnalytics(): Promise<UnsubmittedOrderAnalyticsResponse> {
    const investmentProductsDict = await InvestmentProductService.getInvestmentProductsDict("isin", true, {
      listedOnly: false
    });
    const unsubmittedOrders = await OrderService._getUnsubmittedOrders();

    return {
      lines: Object.fromEntries(
        unsubmittedOrders.map(({ isin }) => [
          investmentProductsDict[isin].commonId,
          OrderService._getAnalyticsForOrders(
            unsubmittedOrders.filter((order) => order.isin == isin),
            investmentProductsDict,
            ["allMatched"]
          )
        ])
      )
    } as unknown as UnsubmittedOrderAnalyticsResponse;
  }

  /**
   * @description Creates wealthkernel order for all order documents with Aggregate submission intent
   * -that are missing wealthkernel info (thus implied that they haven't been submitted to wealthkernel).
   */
  public static async createMissingAggregateWealthkernelOrders(): Promise<void> {
    if (
      !SubmissionWindowUtil.isCurrentTimeWithinAggregateSubmissionWindow("etf") &&
      !SubmissionWindowUtil.isCurrentTimeWithinAggregateSubmissionWindow("stock") &&
      !SubmissionWindowUtil.isCurrentTimeWithinSavingsProductSubmissionWindow()
    ) {
      logger.info("Will not create Wealthkernel orders, as we are not within order aggregate submission windows", {
        module: "OrderService",
        method: "createMissingAggregateWealthkernelOrders"
      });
      return;
    }

    const missingWkOrders = await Order.find({
      activeProviders: ProviderEnum.WEALTHKERNEL,
      status: "Pending",
      submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate([
      {
        path: "transaction",
        populate: {
          path: "portfolio"
        }
      }
    ]);

    // We submit orders sequentially to WK so that we don't overload them with calls (if they receive too many,
    // they might time out but still create the orders).
    for (const order of missingWkOrders) {
      const transaction = order.transaction as TransactionDocument;
      if (!transaction) {
        logger.error(`Transaction could not be populated for order ${order.id}`, {
          module: "OrderService",
          method: "createMissingAggregateWealthkernelOrders",
          data: { orderId: order.id }
        });
        addBreadcrumb({
          level: "error",
          message: `Transaction could not be populated for order ${order.id}`,
          data: { orderId: order.id }
        });
      }

      if (order.isInvestmentOrder) {
        await OrderService._createMissingAggregateWealthkernelInvestmentOrder(order);
      } else if (order.isSavingsOrder) {
        await OrderService._createMissingWealthkernelSavingsOrder(order);
      }
    }
  }

  /**
   * @description Creates wealthkernel order for all order documents with Realtime submission intent
   * -that are missing wealthkernel info (thus implied that they haven't been submitted to wealthkernel).
   *
   * ## Note: This task checks orders that were created at least 10mins ago to avoid race conditions.
   */
  public static async createFallbackRealtimeWealthkernelOrders(): Promise<void> {
    if (
      !SubmissionWindowUtil.isCurrentTimeWithinRealtimeSubmissionWindow("etf") &&
      !SubmissionWindowUtil.isCurrentTimeWithinRealtimeSubmissionWindow("stock")
    ) {
      logger.info("Will not create Wealthkernel orders, as we are not within order realtime submission windows", {
        module: "OrderService",
        method: "createFallbackRealtimeWealthkernelOrders"
      });
      return;
    }

    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
    const missingWkOrders = await Order.find({
      activeProviders: ProviderEnum.WEALTHKERNEL,
      status: "Pending",
      submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.id": { $eq: undefined } }
      ],
      createdAt: { $lte: tenMinutesAgo }
    }).populate([
      {
        path: "transaction",
        populate: {
          path: "portfolio"
        }
      }
    ]);

    // We submit orders sequentially to WK so that we don't overload them with calls (if they receive too many,
    // they might time out but still create the orders).
    for (const order of missingWkOrders) {
      const transaction = order.transaction as TransactionDocument;
      if (!transaction) {
        logger.error(`Transaction could not be populated for order ${order.id}`, {
          module: "OrderService",
          method: "createFallbackRealtimeWealthkernelOrders",
          data: { orderId: order.id }
        });
        addBreadcrumb({
          level: "error",
          message: `Transaction could not be populated for order ${order.id}`,
          data: { orderId: order.id }
        });
      }

      if (order.isInvestmentOrder) {
        await OrderService._createMissingRealtimeWealthkernelInvestmentOrder(order);
      }
    }
  }

  /**
   * @description Calculates the spent cash from the list of orders.
   * @param orders
   * @returns Total cash that has been spent for order buys
   */
  public static calculateSpentCash(orders: OrderDocument[]): Decimal {
    if (!orders.every((order) => order.hasTerminalStatus)) {
      return new Decimal(0);
    }

    return orders
      .filter((order) => order.isMatched && order.side === "Buy")
      .map((order) => Decimal.div(order.consideration.amount, 100))
      .reduce((totalSpentCash, spentCash) => Decimal.add(totalSpentCash, spentCash), new Decimal(0));
  }

  /**
   * @description Calculates the received cash from the list of orders.
   * @param orders
   * @returns Total cash that has been received for order sells
   */
  public static calculateReceivedCash(orders: OrderDocument[]): Decimal {
    if (!orders.every((order) => order.hasTerminalStatus)) {
      return new Decimal(0);
    }

    return orders
      .filter((order) => order.isMatched && order.side === "Sell")
      .map((order) => Decimal.div(order.consideration.amount, 100))
      .reduce((totalReceivedCash, receivedCash) => Decimal.add(totalReceivedCash, receivedCash), new Decimal(0));
  }

  public static async submitRealtimeOrdersSafely(
    orders: OrderDocument[],
    portfolio: PortfolioDocument
  ): Promise<void> {
    const realtimeEligibleOrders = orders.filter(
      (order) => order.isInvestmentOrder && order.submissionIntent === OrderSubmissionIntentEnum.REAL_TIME
    );

    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
    const user = portfolio.owner as UserDocument;

    // As this is happening synchronously on user request, we submit the orders to WK in batches.
    for (let i = 0; i < realtimeEligibleOrders.length; i += REALTIME_ORDER_SUBMISSION_BATCH_SIZE) {
      const orders = realtimeEligibleOrders.slice(i, i + REALTIME_ORDER_SUBMISSION_BATCH_SIZE);

      await Promise.all(
        orders.map(async (order) => {
          try {
            const shouldExecuteOrderRealtime = await OrderService._canOrderBeSubmittedRealtimeToWK(
              order,
              user.currency
            );

            if (shouldExecuteOrderRealtime) {
              return OrderService._createWkOrderAndUpdateDb(order, user, portfolio?.providers?.wealthkernel?.id);
            }
          } catch (err) {
            logger.error(`Could not submit real-time order ${order.id} for ${portfolio.id}`, {
              module: "OrderService",
              method: "submitRealtimeOrdersSafely"
            });
            captureException(err);
          }
        })
      );
    }
  }

  public static async createDbOrder(
    orderData: OrderDTOInterface,
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<OrderDocument> {
    return new Order(orderData).save({ session: options?.session });
  }

  public static async createManyDbOrders(
    ordersData: OrderDTOInterface[],
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<OrderDocument[]> {
    return Order.insertMany(ordersData, { session: options?.session });
  }

  public static async getOrder(id: string, populate: { transaction: boolean }): Promise<OrderDocument> {
    return Order.findById(id).populate(DbUtil.getPopulationString(populate));
  }

  public static async getOrders(filter: OrdersFilter, sort?: string): Promise<OrderDocument[]> {
    const dbFilter = OrderService._createDbOrdersFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    return Order.find(dbFilter, null, options);
  }

  public static async getOrderByWealthkernelId(
    wealthkernelId: string,
    populate = { transaction: false }
  ): Promise<OrderDocument> {
    return await Order.findOne({
      "providers.wealthkernel.id": wealthkernelId
    }).populate(DbUtil.getPopulationString(populate));
  }

  /**
   * Given an asset allocation and an investment amount, returns a list of buy orders
   * that should be created.
   *
   * @param userCurrency
   * @param assetAllocation
   * @param orderAmount
   * @param userCompanyEntity
   * @param investmentProducts
   * @param executeEtfOrdersInRealtime
   */
  public static async getBuyOrdersToCreate(
    userCurrency: currenciesConfig.MainCurrencyType,
    assetAllocation: { [key in investmentUniverseConfig.AssetType]?: number },
    orderAmount: number,
    userCompanyEntity: entitiesConfig.CompanyEntityEnum,
    investmentProducts: InvestmentProductsDictType,
    executeEtfOrdersInRealtime: boolean
  ): Promise<Omit<OrderDTOInterface, "transaction">[]> {
    const adjustedHoldingsPercentage =
      await PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForBuying(
        userCurrency,
        assetAllocation,
        orderAmount
      );

    const filteredHoldingsPercentage: { [key in investmentUniverseConfig.AssetType]?: number } =
      Object.fromEntries(
        Object.entries(adjustedHoldingsPercentage).filter(
          ([, value]: [investmentUniverseConfig.AssetType, number]) => value > 0
        )
      );

    const ordersToCreate: Omit<OrderDTOInterface, "transaction">[] = [];

    let remainingAmount = new Decimal(orderAmount);
    Object.keys(filteredHoldingsPercentage).forEach(
      (assetCommonId: investmentUniverseConfig.AssetType, index: number) => {
        // Calculate order amount
        const holdingPercentage = filteredHoldingsPercentage[assetCommonId];
        // Order amount can't be over the remaining amount
        // We also need to make sure that amount will have only 2 decimals, otherwise wealthkernel request will fail
        const amount =
          index === Object.keys(assetAllocation).length - 1
            ? remainingAmount.mul(100).round().div(100)
            : Decimal.min(
                remainingAmount.mul(100).floor().div(100),
                Decimal.mul(orderAmount, holdingPercentage).div(100).toDecimalPlaces(2)
              );
        remainingAmount = remainingAmount.minus(amount);

        if (amount.greaterThan(0)) {
          const orderDataWithoutSubmissionIntent: Omit<OrderDTOInterface, "transaction" | "submissionIntent"> = {
            isin: investmentUniverseConfig.ASSET_CONFIG[assetCommonId].isin,
            settlementCurrency: userCurrency,
            side: "Buy",
            consideration: {
              currency: userCurrency,
              amount: amount.mul(100).toNumber(),
              originalAmount: amount.mul(100).toNumber()
            },
            activeProviders: ProviderService.getProviders(userCompanyEntity, [ProviderScopeEnum.BROKERAGE])
          };
          const orderData = OrderService.addOrderSubmissionIntent(orderDataWithoutSubmissionIntent, {
            investmentProduct: investmentProducts[orderDataWithoutSubmissionIntent.isin],
            userCurrency,
            executeEtfOrdersInRealtime
          });
          ordersToCreate.push(orderData);
        }
      }
    );

    return ordersToCreate;
  }

  public static getSingleOrderToCreate(
    order: PendingOrderType,
    options: {
      investmentProduct: InvestmentProductDocument;
      userCurrency: currenciesConfig.MainCurrencyType;
      userCompanyEntity: entitiesConfig.CompanyEntityEnum;
      executeEtfOrdersInRealtime?: boolean;
    }
  ): Omit<OrderDTOInterface, "transaction"> {
    const { investmentProduct, userCurrency, userCompanyEntity, executeEtfOrdersInRealtime } = options;

    let orderData: Omit<OrderDTOInterface, "transaction" | "submissionIntent">;
    if (order.side === "buy") {
      const considerationAmount = order.money ? Decimal.mul(order.money, 100).toNumber() : 0;
      orderData = {
        isin: ASSET_CONFIG[investmentProduct.commonId].isin,
        settlementCurrency: userCurrency,
        side: "Buy",
        consideration: {
          currency: userCurrency,
          amount: considerationAmount,
          originalAmount: considerationAmount // Also set original amount, even if it remains the same as consideration.amount
        },
        activeProviders: ProviderService.getProviders(userCompanyEntity, [ProviderScopeEnum.BROKERAGE])
      };
    } else {
      orderData = {
        isin: ASSET_CONFIG[investmentProduct.commonId].isin,
        settlementCurrency: userCurrency,
        side: "Sell",
        consideration: {
          currency: userCurrency
        },
        quantity: order.quantity,
        activeProviders: ProviderService.getProviders(userCompanyEntity, [ProviderScopeEnum.BROKERAGE])
      };
    }

    return OrderService.addOrderSubmissionIntent(orderData, {
      investmentProduct,
      userCurrency,
      executeEtfOrdersInRealtime
    });
  }

  public static addOrderSubmissionIntent(
    orderData: Omit<OrderDTOInterface, "transaction" | "submissionIntent">,
    options: {
      investmentProduct: InvestmentProductDocument;
      userCurrency: currenciesConfig.MainCurrencyType;
      executeEtfOrdersInRealtime?: boolean;
    }
  ): Omit<OrderDTOInterface, "transaction"> {
    return {
      ...orderData,
      submissionIntent: SubmissionWindowUtil.getOrderSubmissionIntent(orderData, options)
    };
  }

  /**
   * @description Adds client-side display properties to an order object.
   *
   * @param user
   * @param orders Array of orders to enhance.
   * @param order
   * @param investmentProduct
   * @param latestFXRatesWithSpread
   * @param transaction Current transaction document for order evaluations.
   * @returns Enhanced array of OrderDocument objects.
   * @private
   */
  public static fillOrderClientDisplayFields(
    user: UserDocument,
    order: OrderDocument,
    investmentProduct: InvestmentProductDocument,
    latestFXRatesWithSpread: PartialRecord<FXSpreadSide, ForeignCurrencyRatesType>,
    transaction: TransactionDocument,
    {
      displayAmount = true,
      displayQuantity = true,
      executionWindow = true,
      displayExchangeRate = false,
      isCancellable = false,
      estimatedRealTimeCommission = false
    }: {
      displayAmount?: boolean;
      displayQuantity?: boolean;
      displayExchangeRate?: boolean;
      isCancellable?: boolean;
      executionWindow?: boolean;
      estimatedRealTimeCommission?: boolean;
    } = {}
  ): OrderDocument {
    const filledOrder = order.toObject();

    if (displayAmount) {
      filledOrder.displayAmount = order.getDisplayAmount(user.currency, investmentProduct, transaction);
    }

    if (displayQuantity) {
      filledOrder.displayQuantity = order.getDisplayQuantity(user.currency, investmentProduct, transaction);
    }

    if (displayExchangeRate) {
      filledOrder.displayExchangeRate = order.getDisplayExchangeRate(
        user,
        investmentProduct,
        order.side === "Buy" ? latestFXRatesWithSpread.BUY : latestFXRatesWithSpread.SELL
      );
    }

    if (isCancellable) {
      filledOrder.isCancellable = order.getIsCancellable(
        user,
        investmentProduct,
        transaction as AssetTransactionDocument | RebalanceTransactionDocument
      );
    }

    if (executionWindow) {
      filledOrder.executionWindow = order.getExecutionWindow(
        user,
        investmentProduct,
        transaction as AssetTransactionDocument | RebalanceTransactionDocument
      );
    }

    if (estimatedRealTimeCommission) {
      filledOrder.estimatedRealTimeCommission = order.getEstimatedRealTimeCommission(user.subscription?.plan);
    }

    return filledOrder as OrderDocument;
  }

  /**
   * Given a portfolio and an order amount, returns a list of sell orders
   * that should be created. A transaction ID must also be passed in non-dry-run
   * operations where we want to later store the returned orders.
   *
   * @param portfolio
   * @param orderAmount
   * @param options
   */
  public static async getSellOrdersToCreate(
    portfolio: PortfolioDocument,
    orderAmount: number,
    options: {
      orderFilteringMethod?: SellOrderFilteringMethodEnum;
      sellRestrictedHoldings?: boolean;
      executeEtfOrdersInRealtime: boolean;
    }
  ): Promise<Omit<OrderDTOInterface, "transaction">[]> {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
    const user = portfolio.owner as UserDocument;

    const {
      orderFilteringMethod = SellOrderFilteringMethodEnum.FILTER_AND_DISCARD,
      sellRestrictedHoldings = false,
      executeEtfOrdersInRealtime = user.isRealtimeETFExecutionEnabled
    } = options;

    const availableHoldings = sellRestrictedHoldings
      ? await PortfolioService.getHoldingsExcludingPendingOrders(portfolio)
      : await PortfolioService.getAvailableHoldings(portfolio);

    const holdingsDict = Object.fromEntries(availableHoldings.map((holding) => [holding.assetCommonId, holding]));

    let ordersToCreate: Omit<OrderDTOInterface, "transaction">[];
    if (
      [
        SellOrderFilteringMethodEnum.FILTER_AND_DISCARD_QUANTITY_ONLY,
        SellOrderFilteringMethodEnum.FILTER_AND_DISCARD
      ].includes(orderFilteringMethod)
    ) {
      const adjustedHoldingsArray: HoldingsType[] =
        orderFilteringMethod === SellOrderFilteringMethodEnum.FILTER_AND_DISCARD
          ? PortfolioUtil.filterSmallQuantityAndAmountHoldings(user.currency, availableHoldings, orderAmount)
          : PortfolioUtil.filterSmallQuantityHoldings(user.currency, availableHoldings, orderAmount);

      const holdingsValue = availableHoldings
        .map(({ quantity, asset }) => Decimal.mul(quantity, asset.currentTicker.getPrice(user.currency)))
        .reduce((sum, amount) => sum.plus(amount), new Decimal(0));

      ordersToCreate = adjustedHoldingsArray.map((holding) => {
        const decOrderAmount = new Decimal(orderAmount);

        const quantity = Decimal.mul(
          holdingsDict[holding.assetCommonId].quantity,
          Decimal.min(1, decOrderAmount.div(holdingsValue))
        ).toDecimalPlaces(4);

        const orderDataWithoutSubmissionIntent: Omit<OrderDTOInterface, "transaction" | "submissionIntent"> = {
          quantity: quantity.toNumber(),
          isin: ASSET_CONFIG[holding.assetCommonId].isin,
          settlementCurrency: user.currency,
          consideration: {
            currency: user.currency
          },
          side: "Sell",
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        };

        return OrderService.addOrderSubmissionIntent(orderDataWithoutSubmissionIntent, {
          investmentProduct: holding.asset,
          userCurrency: user.currency,
          executeEtfOrdersInRealtime: executeEtfOrdersInRealtime
        });
      });
    } else {
      const ordersToBeCreated = PortfolioUtil.filterSmallPercentageHoldingsDistributingAllocationForSelling(
        user.currency,
        availableHoldings,
        orderAmount
      );

      ordersToCreate = Object.entries(ordersToBeCreated).map(([assetCommonId, quantity]) => {
        const orderDataWithoutSubmissionIntent: Omit<OrderDTOInterface, "transaction" | "submissionIntent"> = {
          quantity: new Decimal(quantity).toDecimalPlaces(4).toNumber(),
          isin: ASSET_CONFIG[assetCommonId as investmentUniverseConfig.AssetType].isin,
          settlementCurrency: user.currency,
          consideration: {
            currency: user.currency
          },
          side: "Sell",
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        };

        return OrderService.addOrderSubmissionIntent(orderDataWithoutSubmissionIntent, {
          investmentProduct: holdingsDict[assetCommonId].asset,
          userCurrency: user.currency,
          executeEtfOrdersInRealtime: executeEtfOrdersInRealtime
        });
      });
    }

    return ordersToCreate;
  }

  /**
   * Returns a list of buy orders that will be created for rebalancing this portfolio.
   * An asset transaction ID must also be passed in non-dry-run operations where
   * we want to later store the returned orders.
   *
   * @param userCurrency
   * @param assetsToBuy
   * @param targetAllocation
   * @param receivedCash
   * @param userCompanyEntity
   * @param rebalanceTransactionId
   */
  public static async getRebalanceBuyOrdersToCreate(
    userCurrency: currenciesConfig.MainCurrencyType,
    assetsToBuy: {
      amount: number;
      assetCommonId: investmentUniverseConfig.AssetType;
      asset: InvestmentProductDocument;
    }[],
    targetAllocation: InitialHoldingsAllocationType[],
    receivedCash: number,
    userCompanyEntity: entitiesConfig.CompanyEntityEnum,
    rebalanceTransactionId?: mongoose.Types.ObjectId
  ): Promise<OrderDTOInterface[]> {
    let isRemainingAmount = false;
    const totalValueOfAssetsToBuy = assetsToBuy
      .map((holding) => holding.amount)
      .reduce((total, current) => total.add(current), new Decimal(0));
    const percentageOfAssetsToBuy = assetsToBuy
      .map(
        (holding) =>
          targetAllocation.find((allocation) => allocation.assetCommonId === holding.assetCommonId).percentage
      )
      .reduce((total, current) => total.add(current), new Decimal(0));

    const ordersToCreate: OrderDTOInterface[] = [];

    let remainingAmount = new Decimal(receivedCash);

    assetsToBuy
      .map((assetToBuy, index) => {
        let amountToBuy: Decimal;

        if (index === assetsToBuy.length - 1) {
          // This is the last asset, so we're buying whatever is left from the received cash.
          amountToBuy = remainingAmount.mul(100).floor().div(100);
        } else {
          amountToBuy = Decimal.min(
            Decimal.add(totalValueOfAssetsToBuy, receivedCash)
              .mul(
                Decimal.div(
                  targetAllocation.find((allocation) => allocation.assetCommonId === assetToBuy.assetCommonId)
                    .percentage,
                  percentageOfAssetsToBuy
                )
              )
              .sub(assetsToBuy.find((holding) => holding.assetCommonId === assetToBuy.assetCommonId).amount)
              .toDecimalPlaces(2),
            remainingAmount.mul(100).floor().div(100)
          );
        }

        // If the amount or quantity are below our limits, we keep that amount in remaining cash to be spend in another asset.
        if (
          amountToBuy.greaterThanOrEqualTo(MIN_ALLOWED_REBALANCE_ORDER_AMOUNT) &&
          amountToBuy
            .div(assetToBuy.asset.currentTicker.getPrice(userCurrency))
            .greaterThanOrEqualTo(MIN_ALLOWED_ASSET_QUANTITY)
        ) {
          remainingAmount = remainingAmount.minus(amountToBuy);
          return {
            assetCommonId: assetToBuy.assetCommonId,
            asset: assetToBuy.asset,
            amount: amountToBuy.mul(100)
          };
        } else {
          if (index === assetsToBuy.length - 1) isRemainingAmount = true;
          return null;
        }
      })
      .filter((assetToBuy) => assetToBuy !== null)
      .forEach((assetToBuy) => {
        ordersToCreate.push({
          consideration: {
            currency: userCurrency,
            amount: assetToBuy.amount.toNumber()
          },
          isin: ASSET_CONFIG[assetToBuy.asset.commonId].isin,
          settlementCurrency: userCurrency,
          side: "Buy",
          transaction: rebalanceTransactionId,
          activeProviders: ProviderService.getProviders(userCompanyEntity, [ProviderScopeEnum.BROKERAGE]),
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });
      });

    /*
     * Addresses a scenario involving a remaining amount after order creation.
     * Specifically, it handles cases where the last order created falls within the above restrictions/limits, resulting in an amount left unallocated.
     * To distribute this remaining amount among other orders, we calculate and allocate a proportional share to each order in 'ordersToCreate'.
     */
    if (isRemainingAmount) {
      logger.info(
        `There is a remaining amount of ${remainingAmount} in this rebalance order with id ${rebalanceTransactionId}`,
        {
          method: "getRebalanceBuyOrdersToCreate",
          module: "OrderService"
        }
      );

      const amountToAdd = new Decimal(0.01);
      while (remainingAmount.greaterThan(0)) {
        const originalRemainingAmount = remainingAmount;

        for (let i = 0; i < ordersToCreate.length && remainingAmount.greaterThan(0); i++) {
          ordersToCreate[i].consideration.amount = Decimal.add(
            new Decimal(ordersToCreate[i].consideration.amount),
            amountToAdd.mul(100)
          ).toNumber();

          remainingAmount = remainingAmount.minus(amountToAdd);
        }

        if (remainingAmount === originalRemainingAmount) {
          throw new Error(
            `Rebalance buy orders creation for ${
              rebalanceTransactionId ?? "requested preview"
            } has an unresolved remaining amount that is not getting decreased!`
          );
        }
      }
    }

    // Initialise consideration.originalAmount
    return ordersToCreate.map((order) => ({
      ...order,
      consideration: {
        amount: order.consideration.amount,
        originalAmount: order.consideration.amount,
        currency: order.consideration.currency
      }
    }));
  }

  /**
   * Returns a list of sell orders that will be created for rebalancing this portfolio.
   * An asset transaction ID must also be passed in non-dry-run operations where
   * we want to later store the returned orders.
   *
   * @param portfolio
   * @param targetAllocation
   * @param rebalanceTransactionId
   * @param holdingsToCreateOrdersFor If not defined, will use portfolio.holdings
   */
  public static async getRebalanceSellOrdersToCreate(
    portfolio: PortfolioDocument,
    targetAllocation: InitialHoldingsAllocationType[],
    rebalanceTransactionId?: mongoose.Types.ObjectId,
    holdingsToCreateOrdersFor?: HoldingsType[]
  ): Promise<OrderDTOInterface[]> {
    const holdings = holdingsToCreateOrdersFor ?? portfolio.holdings;

    if (!portfolio.populated("owner")) {
      await portfolio.populate("owner");
    }
    const user = portfolio.owner as UserDocument;
    const availableHoldings = await PortfolioService.getAvailableHoldings(portfolio);
    const targetHoldings = PortfolioUtil.getTargetAllocationInCurrency(user.currency, portfolio, targetAllocation);
    const ordersToCreate: OrderDTOInterface[] = [];

    holdings
      .map((holding) => {
        return {
          assetCommonId: holding.assetCommonId,
          availableQuantity: new Decimal(
            availableHoldings.find(({ assetCommonId }) => assetCommonId === holding.assetCommonId)?.quantity ?? 0
          ),
          currentQuantity: new Decimal(holding.quantity),
          targetQuantity: Decimal.div(
            targetHoldings[holding.assetCommonId] ?? 0,
            holding.asset.currentTicker.getPrice(user.currency)
          ), // Target quantity = Target Amount / Asset Price
          asset: holding.asset
        };
      })
      .map((holdingToSell) => {
        return {
          assetCommonId: holdingToSell.assetCommonId,
          sellQuantity: Decimal.min(
            holdingToSell.availableQuantity,
            holdingToSell.currentQuantity.sub(holdingToSell.targetQuantity)
          ).toDecimalPlaces(4),
          asset: holdingToSell.asset
        };
      })
      .filter((holdingToSell) => {
        const estimatedAmountToSell = Decimal.mul(
          holdingToSell.sellQuantity,
          holdingToSell.asset.currentTicker.getPrice(user.currency)
        );
        return (
          holdingToSell.sellQuantity.greaterThanOrEqualTo(MIN_ALLOWED_ASSET_QUANTITY) &&
          estimatedAmountToSell.greaterThanOrEqualTo(MIN_ALLOWED_REBALANCE_ORDER_AMOUNT)
        );
      })
      .forEach((holdingToSell) => {
        ordersToCreate.push({
          quantity: holdingToSell.sellQuantity.toNumber(),
          isin: ASSET_CONFIG[holdingToSell.asset.commonId].isin,
          settlementCurrency: user.currency,
          side: "Sell",
          consideration: {
            currency: user.currency
          },
          transaction: rebalanceTransactionId,
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE]),
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
        });
      });

    return ordersToCreate;
  }

  /**
   * Calculates the FX, commission and execution spread fee for the given orders & returns the list
   * of updated orders.
   *
   * If list of requested orders include orders with quantity instead of amount, then we estimate the fee that will
   * be applied if those quantities are bought/sold with the current ticker price.
   *
   * @param plan
   * @param orders
   * @param userCurrency is required because we need to determine for which currencies to apply FX
   * @param investmentProductsDict
   */
  public static applyFeesToOrders(
    plan: plansConfig.PlanType,
    orders: Omit<OrderDTOInterface, "transaction">[],
    userCurrency: currenciesConfig.MainCurrencyType,
    investmentProductsDict: { [isin: string]: InvestmentProductDocument }
  ): Omit<OrderDTOInterface, "transaction">[] {
    return orders.map((order) => {
      const fees = OrderService.calculateFeesForSingleOrder(plan, order, {
        investmentProduct: investmentProductsDict[order.isin],
        userCurrency
      });

      return order.side === "Buy"
        ? {
            ...order,
            fees,
            consideration: {
              amount: Decimal.sub(
                order.consideration.amount,
                new Decimal(getTotalFeeAmount(fees)).mul(100)
              ).toNumber(),
              originalAmount: order.consideration?.originalAmount ?? order.consideration.amount,
              currency: userCurrency
            }
          }
        : {
            ...order,
            fees
          };
    });
  }

  /**
   * Deletes all orders with a transaction ID that does not match a transaction document.
   */
  public static async deleteOrphanedOrders(): Promise<void> {
    const orphanedOrders = await Order.aggregate([
      {
        $lookup: {
          from: "transactions",
          localField: "transaction",
          foreignField: "_id",
          as: "transaction"
        }
      },
      {
        $unwind: {
          path: "$transaction",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $match: {
          transaction: {
            $eq: null
          }
        }
      }
    ]);

    await Promise.all(orphanedOrders.map((order) => Order.findByIdAndDelete(order._id)));
  }

  public static async getPendingOrders(
    owner: string,
    options?: { session: mongoose.ClientSession }
  ): Promise<OrderDocument[]> {
    const pendingTransactions = await TransactionService.getTransactionsWithPotentiallyPendingOrders(owner, {
      session: options?.session
    });

    return pendingTransactions
      .flatMap((transaction) => transaction.orders)
      .filter((order) => order.status === "Pending")
      .map((order) => order.toObject());
  }

  public static async getLatestMatchedOrderId(owner: string): Promise<string> {
    const assetTransactions = await TransactionService.getAssetTransactionsWithPotentiallyMatchedOrders(owner);

    const matchedOrders = OrderService.getMatchedOrdersForTransactions({
      assetTransactions: assetTransactions,
      rebalanceTransactions: [],
      chargeTransactions: []
    });

    return matchedOrders.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()).at(0)?.id;
  }

  public static async cancelOrder(order: OrderDocument): Promise<OrderInterface> {
    const [investmentProductsDict] = await Promise.all([
      InvestmentProductService.getInvestmentProductsDict("isin", true),
      DbUtil.populateIfNotAlreadyPopulated(order, OrderPopulationFieldsEnum.TRANSACTION)
    ]);
    const transaction = order.transaction as TransactionWithOrders;

    await DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER);
    const user = transaction.owner as UserDocument;

    if (!order.getIsCancellable(user, investmentProductsDict[order.isin], transaction)) {
      throw new BadRequestError(`Order ${order.id} is not cancellable`);
    }

    const updatedTransaction = await DbUtil.runInSession<AssetTransactionDocument>(
      async (session: mongoose.ClientSession) => {
        await OrderService.setOrderStatusToCancelled(order, user, investmentProductsDict[order.isin], { session });

        // In case the cancelled order has side 'Buy':
        // 1. Make sure the cash we reserved becomes available. We only return cash for pending transactions.
        // 2. If the transaction has a linked cashback transaction, we want to cancel that so that the user doesn't get it
        if (transaction.status === "Pending" && order.side === "Buy") {
          await PortfolioService.updateCashAvailability(
            transaction.portfolio.toString(),
            user.currency,
            new Decimal(order.consideration.originalAmount).div(100).toNumber(),
            { session, available: true, settled: true }
          );

          const otherPendingOrdersOfUser = await OrderService.getPendingOrders(user.id, { session });

          if (user.isConvertingPortfolio && otherPendingOrdersOfUser.length === 0) {
            await UserService.convertUser(user, "notStarted", { session });
          }
        }

        const updatedTransaction = await TransactionService.syncAssetTransaction(
          transaction as AssetTransactionDocument,
          investmentProductsDict,
          { session }
        );

        await TransactionService.reduceCashbackAmountAfterCancelledOrder(
          order.consideration?.originalAmount,
          updatedTransaction,
          { session }
        );

        return updatedTransaction;
      }
    );

    // Populating user's subscriptions separately as we need it to retrieve foreign currency rates with spread.
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION);

    const [latestBuyFXRatesWithSpread, latestSellFXRatesWithSpread] = await Promise.all([
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.BUY),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.SELL),
      DbUtil.populateIfNotAlreadyPopulated(updatedTransaction, TransactionPopulationFieldsEnum.ORDERS)
    ]);

    return {
      ...updatedTransaction.toObject(),
      transaction: TransactionService.fillClientDisplayFields(
        user,
        updatedTransaction,
        investmentProductsDict,
        {
          BUY: latestBuyFXRatesWithSpread,
          SELL: latestSellFXRatesWithSpread
        },
        {
          displayAmount: true,
          displayQuantity: true,
          executionWindow: true,
          displayExchangeRate: true,
          isCancellable: true,
          estimatedRealTimeCommission: true
        }
      ) as AssetTransactionDocument
    };
  }

  public static async setOrderStatusToCancelled(
    order: OrderDocument,
    user: UserDocument,
    investmentProduct: InvestmentProductDocument,
    options?: { session: mongoose.ClientSession }
  ): Promise<OrderDocument> {
    await DbUtil.populateIfNotAlreadyPopulated(order, OrderPopulationFieldsEnum.TRANSACTION);

    if (!order.getIsCancellable(user, investmentProduct, order.transaction as TransactionWithOrders)) {
      return order;
    }

    const cancelledOrder = await Order.findByIdAndUpdate(
      order.id,
      {
        status: "Cancelled"
      },
      { new: true, session: options?.session }
    );

    eventEmitter.emit(events.order.orderCancellation.eventId, user, {
      asset: order.commonId,
      amount: order.side === "Buy" ? Decimal.div(order.consideration?.originalAmount, 100).toNumber() : undefined,
      currency: order.consideration?.currency ?? user.currency,
      quantity: order.quantity,
      side: order.side
    });

    return cancelledOrder;
  }

  public static async createSavingsOrder(
    transaction: SavingsTopupTransactionDocument | SavingsWithdrawalTransactionDocument
  ): Promise<void> {
    const remainingAmount = transaction.remainingAmountToSubmit;
    const minAllowedSavingsInvestment = Decimal.mul(SAVINGS_PRODUCT_MINIMUM_WK_ORDER_AMOUNT, 100).toNumber();

    await DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER);
    const user = transaction.owner as UserDocument;

    if (remainingAmount >= minAllowedSavingsInvestment) {
      const side = transaction.category === "SavingsTopupTransaction" ? "Buy" : "Sell";
      const orderData: OrderDTOInterface = {
        isin: SAVINGS_PRODUCT_CONFIG_GLOBAL[transaction.savingsProduct].isin,
        settlementCurrency: user.currency,
        side: side,
        transaction: transaction._id,
        activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE]),
        consideration: {
          currency: user.currency,
          amount: remainingAmount,
          originalAmount: remainingAmount,
          amountSubmitted: remainingAmount
        },
        fees: getZeroFees(user.currency),
        submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
      };
      await OrderService.createDbOrder(orderData);
    } else if (remainingAmount > 0 && remainingAmount < minAllowedSavingsInvestment) {
      logger.info(`Insufficient amount to create an order for Savings Transaction ${transaction.id}`, {
        module: "OrderService",
        method: "createSavingsOrder"
      });
    }
  }

  public static async getAssetOrderActivityByTenor(
    userId: string,
    assetCommonId: investmentUniverseConfig.AssetType
  ): Promise<Record<TenorEnum, OrderActivityItemType[]>> {
    const transactions = (await Transaction.find({
      $or: [
        {
          owner: userId,
          category: "AssetTransaction",
          status: { $in: ["Pending", "Settled"] }
        },
        {
          owner: userId,
          category: "RebalanceTransaction",
          rebalanceStatus: { $in: ["Pending", "PendingBuy", "PendingSell", "Settled"] }
        }
      ]
    }).populate("orders")) as (AssetTransactionDocument | RebalanceTransactionDocument)[];

    const allAssetIsinsSet = new Set(InvestmentUniverseUtil.getIsinActiveAndDeprecated(assetCommonId));
    const orderActivity: OrderActivityItemType[] = transactions
      .flatMap((transaction) => transaction.orders)
      .filter((order) => allAssetIsinsSet.has(order.isin) && order.isMatched)
      .map((order) => {
        return {
          timestamp: order.filledAt.getTime(),
          side: order.side,
          quantity: order.quantity
        };
      });

    return Object.fromEntries(
      Object.values(TenorEnum).map((tenor) => {
        const timestampAtTenorStart = DateUtil.getStartAndEndOfDay(
          DateUtil.getDateOfDaysAgo(new Date(Date.now()), DURATIONS_MAP[tenor])
        ).start.getTime();

        const data = orderActivity.filter(
          ({ timestamp }: OrderActivityItemType) => timestamp >= timestampAtTenorStart
        );

        return [tenor, data];
      })
    ) as Record<TenorEnum, OrderActivityItemType[]>;
  }

  /**
   * Returns the calculated FX, commission and execution spread fee, real time execution for the given order.
   *
   * If list of orders include orders with quantity instead of amount, then we estimate the fee that will
   * be applied if those quantities are bought/sold with the current ticker price.
   *
   * @param plan
   * @param order
   * @param options
   */
  public static calculateFeesForSingleOrder(
    plan: plansConfig.PlanType,
    order: Omit<OrderDTOInterface, "transaction">,
    options: {
      investmentProduct: InvestmentProductDocument;
      userCurrency: currenciesConfig.MainCurrencyType;
    }
  ): FeesType {
    const originalAmount =
      order.consideration?.amount === undefined || order.consideration?.amount === null
        ? Decimal.mul(
            order.quantity,
            options.investmentProduct.currentTicker.getPrice(options.userCurrency)
          ).toNumber()
        : Decimal.div(order.consideration.amount, 100).toNumber();

    /**
     * Real time execution fee:
     * - Only applied to ETF orders with REAL_TIME submission intent
     * - Only applied if order amount is sufficient
     */
    let realtimeExecutionFee = new Decimal(0);
    const isEtf = ASSET_CONFIG[options.investmentProduct.commonId].category === "etf";
    const realtimeExecutionFeeAmount = new Decimal(REAL_TIME_ETF_EXECUTION_FEES[plan]).toDecimalPlaces(2);
    if (
      isEtf &&
      order.submissionIntent === OrderSubmissionIntentEnum.REAL_TIME &&
      Decimal.sub(originalAmount, realtimeExecutionFeeAmount).greaterThanOrEqualTo(0)
    ) {
      realtimeExecutionFee = realtimeExecutionFeeAmount;
    }

    // We charge the rest of the fees on the amount left after the real time fee is deducted
    const amountAfterRealTimeFee = realtimeExecutionFee.isZero()
      ? originalAmount
      : Decimal.sub(originalAmount, realtimeExecutionFeeAmount);

    let commissionFee = new Decimal(0);
    if (COMMISSION_RATES[plan] > 0) {
      commissionFee = Decimal.max(
        MINIMUM_COMMISSION_FEE,
        Decimal.mul(amountAfterRealTimeFee, COMMISSION_RATES[plan]).toDecimalPlaces(2)
      );
    }

    let executionSpread = new Decimal(0);
    if (EXECUTION_SPREAD_RATES[plan] > 0) {
      executionSpread = Decimal.mul(amountAfterRealTimeFee, EXECUTION_SPREAD_RATES[plan]).toDecimalPlaces(2);
    }

    const isOrderForeignCurrencyTraded = CurrencyUtil.isForeignCurrency(
      options.userCurrency,
      options.investmentProduct.tradedCurrency
    );

    let fxFee = new Decimal(0);
    if (isOrderForeignCurrencyTraded) {
      fxFee = Decimal.mul(
        amountAfterRealTimeFee,
        (FX_FEE_SPREADS_WH as Record<string, number>)[plan]
      ).toDecimalPlaces(2);
    }

    const totalFeeAmountExcludingRealTime = executionSpread.add(fxFee).add(commissionFee);

    // If the fee amount is greater than the amount left after real time fee is deducted, we only
    // charge the real-time execution fee
    if (totalFeeAmountExcludingRealTime.greaterThan(amountAfterRealTimeFee)) {
      executionSpread = new Decimal(0);
      fxFee = new Decimal(0);
      commissionFee = new Decimal(0);
    }

    return {
      fx: {
        amount: fxFee.toNumber(),
        currency: options.userCurrency
      },
      commission: {
        amount: commissionFee.toNumber(),
        currency: options.userCurrency
      },
      executionSpread: {
        amount: executionSpread.toNumber(),
        currency: options.userCurrency
      },
      realtimeExecution: {
        amount: realtimeExecutionFee.toNumber(),
        currency: options.userCurrency
      }
    };
  }

  // PRIVATE METHODS
  /**
   * @returns a list of orders and reward orders that have not been submitted to Wealthkernel. The return type includes
   * Partial because orders that belong to rewards do not contain all the fields inside OrderInterface.
   */
  private static async _getUnsubmittedOrders(): Promise<Partial<OrderInterface>[]> {
    const unsubmittedOrders: Partial<OrderInterface>[] = await Order.find({
      status: "Pending",
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate("transaction");
    const rewardsPendingOrder = (
      await Reward.find({
        "deposit.providers.wealthkernel.status": { $eq: "Settled" },
        $or: [
          { "order.providers.wealthkernel.id": { $exists: false } },
          { "order.providers.wealthkernel.id": { $eq: undefined } }
        ]
      })
    ).map((reward: RewardDocument) => {
      return {
        consideration: reward.consideration,
        isin: ASSET_CONFIG[reward.asset].isin,
        side: "Buy"
      } as Partial<OrderInterface>;
    });

    return unsubmittedOrders
      .filter(
        ({ transaction }) =>
          !["PendingDeposit", "PendingGift", "Cancelled", "Rejected", "DepositFailed"].includes(
            (transaction as TransactionDocument).status
          ) && ["AssetTransaction", "RebalanceTransaction"].includes((transaction as TransactionDocument).category)
      )
      .concat(rewardsPendingOrder);
  }

  private static _createDbOrdersFilter(filter: OrdersFilter) {
    return {
      isin: filter.isin,
      "providers.wealthkernel.submittedAt": {
        $gte: filter.submissionDay,
        $lt: DateUtil.getDateAfterNdays(filter.submissionDay, 1)
      }
    };
  }

  /**
   * @description Handles order creation process. The respected order isin should match an Investment or Savings Product
   * - First sends a request for a new order to Wealthkernel
   * - On successful response, updates the corresponding order document with WK details
   *
   * Notes:
   * - The order amount is in pounds not cents.
   * - Savings Orders are not executed in real time.
   *
   * @returns
   * @param orderToSubmit
   * @param user
   * @param wealthkernelPortfolioId
   */
  private static async _createWkOrderAndUpdateDb(
    orderToSubmit: OrderDocument,
    user: UserDocument,
    wealthkernelPortfolioId: string
  ): Promise<OrderDocument> {
    const orderId = orderToSubmit?._id;
    try {
      const wkOrderData = OrderService._convertToWkOrderData(
        orderToSubmit,
        user.currency,
        wealthkernelPortfolioId
      );
      const shouldExecuteOrderRealtime = await OrderService._canOrderBeSubmittedRealtimeToWK(
        orderToSubmit,
        user.currency
      );
      // 1. Create WK order
      const wealthkernelResponse = await ProviderService.getBrokerageService(user.companyEntity).createOrder(
        wkOrderData,
        orderToSubmit.id,
        { realtime: shouldExecuteOrderRealtime }
      );
      const orderWealthkernelId = wealthkernelResponse.id;

      // 2. Update order in DB with wealthkernel info
      return await Order.findOneAndUpdate(
        {
          _id: orderId
        },
        {
          "consideration.amountSubmitted": orderToSubmit.consideration?.amount ?? undefined,
          "providers.wealthkernel": {
            status: "Pending",
            id: orderWealthkernelId,
            submittedAt: new Date(Date.now())
          }
        },
        {
          new: true
        }
      );
    } catch (error) {
      captureException(error);
      logger.error(`Wealthkernel order creation has failed for orderId: ${orderId}`, {
        module: "OrderService",
        method: "_createWkOrderAndUpdateDb",
        data: { error: error }
      });
    }
  }

  private static _convertToWkOrderData(
    order: OrderDocument,
    userCurrency: currenciesConfig.MainCurrencyType,
    wealthkernelPortfolioId: string
  ): OrderDataType {
    const orderData: OrderDataType = {
      portfolioId: wealthkernelPortfolioId,
      isin: order.isin,
      settlementCurrency: MainCurrencyToWealthkernelCurrency[userCurrency],
      side: order.side
    };

    if (order.side === "Buy") {
      orderData.consideration = {
        currency: MainCurrencyToWealthkernelCurrency[order.consideration.currency],
        amount: order.consideration.amount / 100
      };
    } else if (order.side === "Sell") {
      if (order.isInvestmentOrder) {
        orderData.quantity = order.quantity;
      } else if (order.isSavingsOrder) {
        /**
         * For Savings Orders we divided quantity to the consideration amount / 100.
         * Because MMFs are traded in £1, €1, $1 etc.
         */
        orderData.quantity = order.consideration.amount / 100;
      }
    }

    return orderData;
  }

  private static _getAnalyticsForOrders(
    orders: Partial<OrderInterface>[],
    investmentProductsDict: { [key: string]: InvestmentProductDocument },
    excludeAnalytics: AnalyticType[] = []
  ): {
    buyOrders: number;
    buyConsideration: number;
    sellOrders: number;
    sellConsideration: number;
    allMatched: boolean;
    allRejected: boolean;
    allInTerminalState: boolean;
  } {
    const orderAnalytics = orders.reduce(
      (
        {
          buyOrders,
          buyConsideration,
          sellOrders,
          sellConsideration,
          allMatched,
          allRejected,
          allInTerminalState
        },
        order
      ) => ({
        buyOrders: order.side === "Buy" ? buyOrders + 1 : buyOrders,
        buyConsideration:
          order.side === "Buy"
            ? Decimal.add(buyConsideration, new Decimal(order.consideration.amount).div(100)).toNumber()
            : buyConsideration,
        sellOrders: order.side === "Sell" ? sellOrders + 1 : sellOrders,
        sellConsideration:
          order.side === "Sell"
            ? Decimal.add(
                sellConsideration,
                order.consideration?.amount
                  ? Decimal.div(order.consideration.amount, 100)
                  : Decimal.mul(
                      order.quantity,
                      investmentProductsDict[order.isin].currentTicker.pricePerCurrency["GBP"]
                    )
              ).toNumber()
            : sellConsideration,
        allMatched: allMatched && order.providers?.wealthkernel?.status === "Matched",
        allRejected: allRejected && order.providers?.wealthkernel?.status === "Rejected",
        allInTerminalState:
          allInTerminalState &&
          (["Matched", "Rejected", "Cancelled"] as WealthkernelOrderStatusType[]).includes(
            order.providers?.wealthkernel?.status
          )
      }),
      {
        buyOrders: 0,
        buyConsideration: 0,
        sellOrders: 0,
        sellConsideration: 0,
        allMatched: true,
        allRejected: true,
        allInTerminalState: true
      }
    );

    return Object.fromEntries(
      Object.entries(orderAnalytics).filter(([key]) => !excludeAnalytics.includes(key as AnalyticType))
    ) as {
      buyOrders: number;
      buyConsideration: number;
      sellOrders: number;
      sellConsideration: number;
      allMatched: boolean;
      allRejected: boolean;
      allInTerminalState: boolean;
    };
  }

  private static async _createMissingAggregateWealthkernelInvestmentOrder(order: OrderDocument): Promise<void> {
    if (!SubmissionWindowUtil.isCurrentTimeWithinAggregateSubmissionWindow(order.assetCategory)) {
      return;
    }

    // Reaching this means that either the order is realtime-enabled, and we are within stock execution
    // market hours, OR the order is realtime-disabled, and we are within ETF order submission hours.
    const transaction = order.transaction as TransactionDocument;
    const assetLineIsActive = await InvestmentProductService.isActive(
      order.commonId as investmentUniverseConfig.AssetType,
      order.side
    );

    // If transaction is a charge, we don't care about the asset line.
    if (transaction.status === "Pending" && (assetLineIsActive || transaction.category === "ChargeTransaction")) {
      const portfolio = transaction.portfolio as PortfolioDocument;

      await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
      const user = portfolio.owner as UserDocument;

      await OrderService._createWkOrderAndUpdateDb(order, user, portfolio?.providers?.wealthkernel?.id);
    }
  }

  private static async _createMissingRealtimeWealthkernelInvestmentOrder(order: OrderDocument): Promise<void> {
    if (!SubmissionWindowUtil.isCurrentTimeWithinRealtimeSubmissionWindow(order.assetCategory)) {
      return;
    }

    // Reaching this means that either the order is realtime-enabled, and we are within stock execution
    // market hours, OR the order is realtime-disabled, and we are within ETF order submission hours.
    const transaction = order.transaction as TransactionDocument;
    const assetLineIsActive = await InvestmentProductService.isActive(
      order.commonId as investmentUniverseConfig.AssetType,
      order.side
    );

    // If transaction is a charge, we don't care about the asset line.
    if (transaction.status === "Pending" && (assetLineIsActive || transaction.category === "ChargeTransaction")) {
      const portfolio = transaction.portfolio as PortfolioDocument;

      await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
      const user = portfolio.owner as UserDocument;

      await OrderService._createWkOrderAndUpdateDb(order, user, portfolio?.providers?.wealthkernel?.id);
    }
  }

  private static async _createMissingWealthkernelSavingsOrder(order: OrderDocument): Promise<void> {
    if (!SubmissionWindowUtil.isCurrentTimeWithinSavingsProductSubmissionWindow()) return;

    const transaction = order.transaction as TransactionDocument;
    const savingsProductLineIsActive = await SavingsProductService.isActive(
      order.commonId as savingsUniverseConfig.SavingsProductType,
      order.side
    );

    if (transaction.status === "Pending" && savingsProductLineIsActive) {
      const portfolio = transaction.portfolio as PortfolioDocument;

      await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
      const user = portfolio.owner as UserDocument;

      await OrderService._createWkOrderAndUpdateDb(order, user, portfolio?.providers?.wealthkernel?.id);
    }
  }

  /**
   * When determining whether the order can be submitted as realtime to WK, we cannot entirely rely on the submission
   * intent of the order document. That is because some things may have changed since the order was created with
   * a REAL_TIME submission intent.
   *
   * 1. We need to check for the time window as an order could have been marked as REAL_TIME when created, but then got
   * delayed, for example due to it being part of a one-step investment where the deposit was made with a bank account
   * pending verification.
   * 2. We need to check that it is not less than our minimum realtime sell order amount as the estimated sell proceeds
   * may have changed since the order was created.
   *
   * @param order
   * @param userCurrency
   * @private
   */
  private static async _canOrderBeSubmittedRealtimeToWK(
    order: OrderDocument,
    userCurrency: currenciesConfig.MainCurrencyType
  ): Promise<boolean> {
    // If it's a Savings order, we never submit it in real time.
    if (order.isSavingsOrder) {
      return false;
    }

    // For investment orders, we first check our investmentUniverse config to see if the asset is aggregated.
    if (ASSET_CONFIG[order.commonId as investmentUniverseConfig.AssetType].aggregatedSubmission) {
      return false;
    }

    /**
     * If it's an ETF order, we only submit it in real time if it has a REAL_TIME submission intent and we are within
     * the realtime ETF submission window.
     */
    if (order.assetCategory === "etf") {
      return (
        order.submissionIntent === OrderSubmissionIntentEnum.REAL_TIME &&
        SubmissionWindowUtil.isCurrentTimeWithinRealtimeSubmissionWindow("etf")
      );
    }

    /**
     * For stock orders:
     * 1. If it has a REAL_TIME submission intent, we submit it in real time if we are within the realtime stock submission window.
     * 2. If it has a AGGREGATE submission intent, we submit it in real time if we are within the aggregate stock submission window
     * AND the estimated amount is above the minimum realtime amount.
     */
    if (order.submissionIntent === OrderSubmissionIntentEnum.REAL_TIME) {
      return SubmissionWindowUtil.isCurrentTimeWithinRealtimeSubmissionWindow("stock");
    }

    const investmentProduct = await InvestmentProductService.getInvestmentProduct(order.commonId, true);
    const isOrderEstimatedAmountBelowMinimumRealtimeAmount =
      SubmissionWindowUtil.isOrderEstimatedAmountBelowMinimumRealtimeAmount(order, {
        assetCategory: order.assetCategory,
        userCurrency,
        investmentProduct
      });

    return (
      !isOrderEstimatedAmountBelowMinimumRealtimeAmount &&
      SubmissionWindowUtil.isCurrentTimeWithinAggregateSubmissionWindow("stock")
    );
  }

  /**
   * @description Checks if the transition from the old status to the new one is valid.
   */
  private static _canTransitionFromStatusToWkStatus(
    oldStatus: OrderStatusType,
    newStatus: WealthkernelOrderStatusWithSettledType
  ): boolean {
    const validTransitions: Record<OrderStatusType, WealthkernelOrderStatusWithSettledType[]> = {
      Pending: ["Matched", "Rejected", "Cancelled"],
      Matched: ["Settled"],
      Settled: [], // terminal
      Rejected: [], // terminal
      Cancelled: [], // terminal
      InternallyFilled: [] // terminal
    };

    return validTransitions[oldStatus]?.includes(newStatus) || false;
  }
}

export default OrderService;
