import Decimal from "decimal.js/decimal";
import { writeFileSync, mkdirSync } from "fs";
import { join } from "path";
import * as path from "path";
import { create } from "xmlbuilder2";
import { stringify } from "csv-stringify/sync";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry,
  LedgerQueryResult
} from "../external-services/accountingLedgerStorageService";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";
import DateUtil from "../utils/dateUtil";
import { ledgerAccountNames } from "../configs/accountingConfig";
import { numberToTwoDecimalsStr } from "../utils/formatterUtil";
import { LedgerAccounts, ACCOUNTING_CUSTOMER_CONFIG } from "../types/accounting";
import { AssetTransactionDocument } from "../models/Transaction";
import { UserDocument } from "../models/User";
import { InvoiceReferenceNumber, InvoiceReferenceNumberDocument } from "../models/InvoiceReferenceNumber";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { getAssetIdFromIsin } from "../utils/investmentUniverseUtil";
import CloudflareService, { BucketsEnum, ContentTypeEnum } from "../external-services/cloudflareService";
import { Readable } from "stream";

const { ASSET_CONFIG } = investmentUniverseConfig;

export const REPORT_TYPES = {
  ACCOUNTING_LEDGER: "accounting_ledger",
  INVOICE_REFERENCE: "invoice_reference"
} as const;

export class AccountingReportingService {
  private static readonly DEFAULT_OUTPUT_DIR = path.join(__dirname, "../scripts/accounting/reports");

  /**
   * Generates XML accounting report file from ledger entries.
   *
   * @param options Configuration options for report generation
   * @param options.fromDate Start date for report (optional)
   * @param options.toDate End date for report (optional)
   * @param options.outputDir Output directory for local files
   * @param options.uploadToCloud Whether to upload to cloud storage
   * @param options.forceFullGeneration Whether to process all entries regardless of checkpoint
   * @param options.updateCheckpoint Whether to update the last processed checkpoint after successful generation (default: true for incremental, false for force/custom dates)
   * @returns Promise resolving to file path or cloud URI
   */
  public static async generateXMLAccountingReportFile(
    options: {
      fromDate?: Date;
      toDate?: Date;
      outputDir?: string;
      uploadToCloud?: boolean;
      forceFullGeneration?: boolean;
      updateCheckpoint?: boolean;
    } = {}
  ): Promise<string> {
    const {
      fromDate,
      toDate,
      outputDir = AccountingReportingService.DEFAULT_OUTPUT_DIR,
      uploadToCloud = true,
      forceFullGeneration = false,
      updateCheckpoint = false
    } = options;
    // Determine if custom dates were provided
    const hasCustomDates = fromDate !== undefined && toDate !== undefined;

    // Default to the whole current day if no window provided and not using incremental processing
    const windowStart: Date = fromDate ?? DateUtil.getStartOfDay(new Date(Date.now()));
    const windowEnd: Date = toDate ?? DateUtil.getEndOfDay(new Date(Date.now()));

    try {
      logger.info(
        `Starting accounting XML report generation from ${windowStart.toISOString()} to ${windowEnd.toISOString()}`,
        {
          module: "AccountingReportingService",
          method: "generateXMLAccountingReportFile",
          data: { forceFullGeneration, hasCustomDates, updateCheckpoint }
        }
      );

      let entries: LedgerQueryResult[];

      if (forceFullGeneration) {
        // Force full regeneration: process ALL entries regardless of date
        entries = await AccountingLedgerStorageService.queryLedgerEntries({});
        logger.info(
          `Force full regeneration: processing ${entries.length} entries (checkpoint update: ${updateCheckpoint})`,
          {
            module: "AccountingReportingService",
            method: "generateXMLAccountingReportFile"
          }
        );
      } else if (hasCustomDates) {
        // Custom dates specified: process entries for date range
        const articleDateFrom = DateUtil.formatDateToYYYYMMDD(windowStart);
        const articleDateTo = DateUtil.formatDateToYYYYMMDD(windowEnd);

        entries = await AccountingLedgerStorageService.queryLedgerEntries({
          article_date_from: articleDateFrom,
          article_date_to: articleDateTo
        });
        logger.info(
          `Custom date range: processing ${entries.length} entries (checkpoint update: ${updateCheckpoint})`,
          {
            module: "AccountingReportingService",
            method: "generateXMLAccountingReportFile"
          }
        );
      } else {
        // Incremental processing: use last checkpoint
        const lastProcessedId = await AccountingLedgerStorageService.getLastProcessedId(
          REPORT_TYPES.ACCOUNTING_LEDGER
        );

        if (lastProcessedId !== null) {
          // Incremental processing: query entries from the last processed ID
          entries = await AccountingLedgerStorageService.queryLedgerEntriesFromId({
            fromId: lastProcessedId
          });
          logger.info(
            `Incremental processing: found ${entries.length} new entries since ID ${lastProcessedId} (checkpoint update: ${updateCheckpoint})`,
            {
              module: "AccountingReportingService",
              method: "generateXMLAccountingReportFile"
            }
          );
        } else {
          // First run: process all entries for current day
          const articleDateFrom = DateUtil.formatDateToYYYYMMDD(windowStart);
          const articleDateTo = DateUtil.formatDateToYYYYMMDD(windowEnd);

          entries = await AccountingLedgerStorageService.queryLedgerEntries({
            article_date_from: articleDateFrom,
            article_date_to: articleDateTo
          });
          logger.info(
            `First run: processing ${entries.length} entries for current day (checkpoint update: ${updateCheckpoint})`,
            {
              module: "AccountingReportingService",
              method: "generateXMLAccountingReportFile"
            }
          );
        }
      }

      // Return empty string if no entries to process
      if (entries.length === 0) {
        logger.info("No entries found to process for XML report", {
          module: "AccountingReportingService",
          method: "generateXMLAccountingReportFile"
        });
        return "";
      }

      // Generate XML content
      const xmlContent = AccountingReportingService._generateAccountingXml(entries);

      // Generate filename with appropriate prefix
      const prefix = forceFullGeneration ? "full_" : "incr_";
      const filename = AccountingReportingService._generateFilename(windowEnd, prefix);

      let result: string;

      if (uploadToCloud) {
        // Upload to Cloudflare R2
        const objectKey = `xml/${filename}`;
        const contentStream = Readable.from([xmlContent]);

        const { fileUri } = await CloudflareService.Instance.uploadObject(
          BucketsEnum.ACCOUNTING_REPORTS,
          objectKey,
          contentStream,
          { contentType: ContentTypeEnum.APPLICATION_XML }
        );

        result = fileUri;
        logger.info(`Accounting XML report uploaded successfully to ${fileUri} with ${entries.length} entries`, {
          module: "AccountingReportingService",
          method: "generateXMLAccountingReportFile"
        });
      } else {
        // Save locally
        mkdirSync(outputDir, { recursive: true });
        const filePath = join(outputDir, filename);
        writeFileSync(filePath, xmlContent, "utf-8");

        result = filePath;
        logger.info(`Accounting report generated successfully at ${filePath} with ${entries.length} entries`, {
          module: "AccountingReportingService",
          method: "generateXMLAccountingReportFile"
        });
      }

      // Update checkpoint after successful generation (only if should update checkpoint)
      if (entries.length > 0 && updateCheckpoint) {
        const latestId = Math.max(...entries.map((entry) => entry.id!));
        await AccountingLedgerStorageService.updateLastProcessedId(REPORT_TYPES.ACCOUNTING_LEDGER, latestId);
        logger.info(`Updated checkpoint to ID ${latestId}`, {
          module: "AccountingReportingService",
          method: "generateXMLAccountingReportFile"
        });
      }

      return result;
    } catch (error) {
      captureException(error);
      logger.error(
        `Failed to generate accounting report for date: ${windowStart.toISOString()} to ${windowEnd.toISOString()}`,
        {
          module: "AccountingReportingService",
          method: "generateXMLAccountingReportFile",
          data: {
            error,
            options
          }
        }
      );
    }
  }

  public static async generateCSVAccountingReportFile(
    options: {
      fromDate?: Date;
      toDate?: Date;
      outputDir?: string;
      uploadToCloud?: boolean;
      forceFullGeneration?: boolean;
    } = {}
  ): Promise<string> {
    const {
      fromDate,
      toDate,
      outputDir = AccountingReportingService.DEFAULT_OUTPUT_DIR,
      uploadToCloud = true,
      forceFullGeneration = false
    } = options;

    // Determine if custom dates were provided
    const hasCustomDates = fromDate !== undefined && toDate !== undefined;

    // Default to the whole current day if no window provided and not using incremental processing
    const windowStart: Date = fromDate ?? DateUtil.getStartOfDay(new Date(Date.now()));
    const windowEnd: Date = toDate ?? DateUtil.getEndOfDay(new Date(Date.now()));

    try {
      logger.info(
        `Starting accounting CSV report generation from ${windowStart.toISOString()} to ${windowEnd.toISOString()}`,
        {
          module: "AccountingReportingService",
          method: "generateCSVAccountingReportFile",
          data: { forceFullGeneration, hasCustomDates }
        }
      );

      let entries: LedgerQueryResult[];

      if (forceFullGeneration) {
        // Force full regeneration: process ALL entries regardless of date
        entries = await AccountingLedgerStorageService.queryLedgerEntries({});
        logger.info(`Force full regeneration: processing ${entries.length} entries`, {
          module: "AccountingReportingService",
          method: "generateCSVAccountingReportFile"
        });
      } else if (hasCustomDates) {
        // Custom dates specified: process entries for date range
        const articleDateFrom = DateUtil.formatDateToYYYYMMDD(windowStart);
        const articleDateTo = DateUtil.formatDateToYYYYMMDD(windowEnd);

        entries = await AccountingLedgerStorageService.queryLedgerEntries({
          article_date_from: articleDateFrom,
          article_date_to: articleDateTo
        });
        logger.info(`Custom date range: processing ${entries.length} entries`, {
          module: "AccountingReportingService",
          method: "generateCSVAccountingReportFile"
        });
      } else {
        // Incremental processing: use last checkpoint
        const lastProcessedId = await AccountingLedgerStorageService.getLastProcessedId(
          REPORT_TYPES.ACCOUNTING_LEDGER
        );

        if (lastProcessedId !== null) {
          // Incremental processing: query entries from the last processed ID
          entries = await AccountingLedgerStorageService.queryLedgerEntriesFromId({
            fromId: lastProcessedId
          });
        } else {
          // First run: process all entries for current day
          const articleDateFrom = DateUtil.formatDateToYYYYMMDD(windowStart);
          const articleDateTo = DateUtil.formatDateToYYYYMMDD(windowEnd);

          entries = await AccountingLedgerStorageService.queryLedgerEntries({
            article_date_from: articleDateFrom,
            article_date_to: articleDateTo
          });
        }
      }

      if (entries.length === 0) {
        logger.info("No entries found to process for CSV report", {
          module: "AccountingReportingService",
          method: "generateCSVAccountingReportFile"
        });
      }

      // Generate CSV content
      const csvContent = AccountingReportingService._generateAccountingCsv(entries);

      // Generate filename with appropriate prefix
      const prefix = forceFullGeneration ? "full_" : "incr_";
      const filename = AccountingReportingService._generateCsvFilename(windowEnd, prefix);

      let result: string;

      if (uploadToCloud) {
        // Upload to Cloudflare R2
        const objectKey = `csv/${filename}`;
        const contentStream = Readable.from([csvContent]);

        const { fileUri } = await CloudflareService.Instance.uploadObject(
          BucketsEnum.ACCOUNTING_REPORTS,
          objectKey,
          contentStream,
          { contentType: ContentTypeEnum.TEXT_CSV }
        );

        result = fileUri;
        logger.info(`Accounting CSV report uploaded successfully to ${fileUri} with ${entries.length} entries`, {
          module: "AccountingReportingService",
          method: "generateCSVAccountingReportFile"
        });
      } else {
        // Save locally
        mkdirSync(outputDir, { recursive: true });
        const filePath = join(outputDir, filename);
        writeFileSync(filePath, csvContent, "utf-8");

        result = filePath;
        logger.info(`Accounting CSV report generated successfully at ${filePath} with ${entries.length} entries`, {
          module: "AccountingReportingService",
          method: "generateCSVAccountingReportFile"
        });
      }

      return result;
    } catch (error) {
      captureException(error);
      logger.error(
        `Failed to generate accounting CSV report from ${windowStart.toISOString()} to ${windowEnd.toISOString()}`,
        {
          module: "AccountingReportingService",
          method: "generateCSVAccountingReportFile",
          data: {
            error,
            options
          }
        }
      );
    }
  }

  public static async generateInvoiceCsvReportFile(
    options: {
      fromDate?: Date;
      toDate?: Date;
      outputDir?: string;
      uploadToCloud?: boolean;
      forceFullGeneration?: boolean;
    } = {}
  ): Promise<string> {
    const {
      fromDate,
      toDate,
      outputDir = AccountingReportingService.DEFAULT_OUTPUT_DIR,
      uploadToCloud = true,
      forceFullGeneration = false
    } = options;

    // Default to the whole current day if no window provided
    const windowStart: Date = fromDate ?? DateUtil.getStartOfDay(new Date(Date.now()));
    const windowEnd: Date = toDate ?? DateUtil.getEndOfDay(new Date(Date.now()));

    try {
      logger.info(
        `Starting invoice CSV report generation from ${windowStart.toISOString()} to ${windowEnd.toISOString()}`,
        {
          module: "AccountingReportingService",
          method: "generateInvoiceCsvReportFile"
        }
      );

      let invoiceReferenceNumbers: InvoiceReferenceNumberDocument[];

      if (forceFullGeneration) {
        // Force full regeneration: process ALL invoice entries regardless of date
        invoiceReferenceNumbers = await InvoiceReferenceNumber.find({
          sourceDocumentType: "Order"
        })
          .sort({ invoiceId: 1 })
          .populate({
            path: "linkedOrder",
            populate: {
              path: "transaction",
              populate: {
                path: "owner"
              }
            }
          });
        logger.info(`Force full regeneration: processing ${invoiceReferenceNumbers.length} invoice entries`, {
          module: "AccountingReportingService",
          method: "generateInvoiceCsvReportFile"
        });
      } else {
        // Get the last processed ID for incremental processing
        const lastProcessedInvoiceId = await AccountingLedgerStorageService.getLastProcessedId(
          REPORT_TYPES.INVOICE_REFERENCE
        );

        if (lastProcessedInvoiceId !== null) {
          // Incremental processing: query entries from the last processed invoice ID
          invoiceReferenceNumbers = await InvoiceReferenceNumber.find({
            sourceDocumentType: "Order",
            invoiceId: { $gt: lastProcessedInvoiceId },
            createdAt: {
              $gte: windowStart,
              $lte: windowEnd
            }
          })
            .sort({ invoiceId: 1 })
            .populate({
              path: "linkedOrder",
              populate: {
                path: "transaction",
                populate: {
                  path: "owner"
                }
              }
            });
          logger.info(
            `Incremental processing: found ${invoiceReferenceNumbers.length} new invoice entries since ID ${lastProcessedInvoiceId}`,
            {
              module: "AccountingReportingService",
              method: "generateInvoiceCsvReportFile"
            }
          );
        } else {
          // First run: process all entries
          invoiceReferenceNumbers = await InvoiceReferenceNumber.find({
            sourceDocumentType: "Order",
            createdAt: {
              $gte: windowStart,
              $lte: windowEnd
            }
          })
            .sort({ invoiceId: 1 })
            .populate({
              path: "linkedOrder",
              populate: {
                path: "transaction",
                populate: {
                  path: "owner"
                }
              }
            });
          logger.info(`First run: processing ${invoiceReferenceNumbers.length} invoice entries`, {
            module: "AccountingReportingService",
            method: "generateInvoiceCsvReportFile"
          });
        }
      }

      // Generate invoice CSV content
      const csvContent = await AccountingReportingService._generateInvoiceCsv(invoiceReferenceNumbers);

      // Generate filename with appropriate prefix
      const prefix = forceFullGeneration ? "full_" : "incr_";
      const filename = AccountingReportingService._generateInvoiceCsvFilename(windowEnd, prefix);

      let result: string;

      if (uploadToCloud) {
        // Upload to Cloudflare R2
        const objectKey = `invoice-csv/${filename}`;
        const contentStream = Readable.from([csvContent]);

        const { fileUri } = await CloudflareService.Instance.uploadObject(
          BucketsEnum.ACCOUNTING_REPORTS,
          objectKey,
          contentStream,
          { contentType: ContentTypeEnum.TEXT_CSV }
        );

        result = fileUri;
        logger.info(
          `Invoice CSV report uploaded successfully to ${fileUri} with ${invoiceReferenceNumbers.length} invoice entries`,
          {
            module: "AccountingReportingService",
            method: "generateInvoiceCsvReportFile"
          }
        );
      } else {
        // Save locally
        mkdirSync(outputDir, { recursive: true });
        const filePath = join(outputDir, filename);
        writeFileSync(filePath, csvContent, "utf-8");

        result = filePath;
        logger.info(
          `Invoice CSV report generated successfully at ${filePath} with ${invoiceReferenceNumbers.length} invoice entries`,
          {
            module: "AccountingReportingService",
            method: "generateInvoiceCsvReportFile"
          }
        );
      }

      // Update checkpoint after successful generation (only if we processed entries)
      if (invoiceReferenceNumbers.length > 0) {
        const latestInvoiceId = Math.max(...invoiceReferenceNumbers.map((invoice) => invoice.invoiceId));
        await AccountingLedgerStorageService.updateLastProcessedId(
          REPORT_TYPES.INVOICE_REFERENCE,
          latestInvoiceId
        );
        logger.info(`Updated invoice checkpoint to ID ${latestInvoiceId}`, {
          module: "AccountingReportingService",
          method: "generateInvoiceCsvReportFile"
        });
      }

      return result;
    } catch (error) {
      captureException(error);
      logger.error(
        `Failed to generate invoice CSV report from ${windowStart.toISOString()} to ${windowEnd.toISOString()}`,
        {
          module: "AccountingReportingService",
          method: "generateInvoiceCsvReportFile",
          data: {
            error,
            options
          }
        }
      );
    }
  }

  private static _generateAccountingCsv(entries: AccountingLedgerEntry[]): string {
    const csvData: string[][] = [];

    // Create a row for each entry with fields matching AccountingLedgerEntry structure
    entries.forEach((entry: AccountingLedgerEntry) => {
      const rowData = [
        entry.aa.toString(),
        entry.reference_number || "",
        entry.article_date,
        entry.description,
        entry.amount.toString(),
        entry.side,
        entry.account_code
      ];

      csvData.push(rowData);
    });

    return stringify(csvData, {
      header: true,
      columns: ["article_index", "invoice_number", "article_date", "description", "amount", "side", "account_code"]
    });
  }

  private static async _generateInvoiceCsv(
    invoiceReferenceNumbers: InvoiceReferenceNumberDocument[]
  ): Promise<string> {
    const csvData: string[][] = [];

    // Process each invoice reference number in order
    for (const invoiceRef of invoiceReferenceNumbers) {
      const order = invoiceRef.linkedOrder;
      const transaction = order.transaction as AssetTransactionDocument;
      const user = transaction?.owner as UserDocument; // owner is already populated
      const assetId = getAssetIdFromIsin(order.isin);
      const assetConfig = ASSET_CONFIG[assetId];

      // Use the auto-incremented invoiceId as the Reference ID
      const referenceId = invoiceRef.invoiceId;

      // Get broker order ID
      const orderId = order.userFriendlyId;

      // Order dates
      const updatedAt = invoiceRef.createdAt.toISOString();
      const executedAt = order.filledAt.toISOString();

      // User details
      const userId = user.id;
      const userName = `${user.firstName} ${user.lastName}`;
      const email = user.email;

      // Instrument details
      const isin = order.isin;
      const symbol = assetConfig.formalTicker;
      const exchange = assetConfig.formalExchange;

      // Order details
      const orderType = "Market";
      const settlementCurrency = order.settlementCurrency;
      const side = order.side;
      const quantity = order.quantity;

      // Order amounts
      const amount = Decimal.div(order.consideration.amount, 100).toFixed(2);
      const unitPrice = order.unitPrice.amount;

      // Fee amounts
      const fxRate = order.exchangeRate;
      // accounting broker FX fee amount + Wealthyhood FX fee amount
      const accountingFxFeeAmount = Decimal.add(
        order.providers.wealthkernel.accountingBrokerFxFee,
        order.fees.fx.amount
      )
        .toDecimalPlaces(2)
        .toNumber();
      const realtimeExecutionFeeAmount = order.fees.realtimeExecution.amount;

      const rowData = [
        referenceId,
        orderId,
        updatedAt,
        executedAt,
        userId,
        userName,
        email,
        isin,
        symbol,
        exchange,
        orderType,
        settlementCurrency,
        side,
        quantity,
        amount,
        unitPrice,
        fxRate,
        accountingFxFeeAmount,
        realtimeExecutionFeeAmount
      ];

      csvData.push(rowData);
    }

    return stringify(csvData, {
      header: true,
      columns: [
        "Reference ID",
        "Order ID",
        "Updated at (date)",
        "Executed at (date)",
        "User ID",
        "User Name",
        "Email",
        "ISIN",
        "Symbol",
        "Exchange",
        "Order Type",
        "Settlement Currency",
        "Side",
        "Quantity",
        "Amount",
        "Unit Price",
        "FX Rate",
        "FX Fee Amount",
        "Commission Fee Amount"
      ]
    });
  }

  private static _generateAccountingXml(entries: AccountingLedgerEntry[]): string {
    const groupedEntries = AccountingReportingService._groupEntriesByAa(entries);
    const uniqueAccounts = AccountingReportingService._extractUniqueAccounts(entries);

    const doc = create({ version: "1.0", encoding: "utf-8" }).ele("DATA", {
      ModuleCode: "3",
      FileType: "GL"
    });

    AccountingReportingService._addArticlesSection(doc, groupedEntries);
    AccountingReportingService._addAccountsSection(doc, uniqueAccounts);

    // Add customers section only if we have entries with reference numbers (invoiced transactions)
    const hasInvoicedTransactions = entries.some(
      (entry) => entry.reference_number && entry.reference_number !== ""
    );
    if (hasInvoicedTransactions) {
      AccountingReportingService._addCustomersSection(doc);
    }

    return doc.end({ prettyPrint: true });
  }

  private static _groupEntriesByAa(entries: AccountingLedgerEntry[]): {
    [aa: number]: AccountingLedgerEntry[];
  } {
    return entries.reduce(
      (acc, entry) => {
        if (!acc[entry.aa]) {
          acc[entry.aa] = [];
        }
        acc[entry.aa].push(entry);
        return acc;
      },
      {} as { [aa: number]: AccountingLedgerEntry[] }
    );
  }

  private static _extractUniqueAccounts(entries: AccountingLedgerEntry[]): string[] {
    const accountCodes = new Set<string>();
    entries.forEach((entry: AccountingLedgerEntry) => {
      accountCodes.add(entry.account_code);
    });
    return Array.from(accountCodes).sort();
  }

  private static _addArticlesSection(
    parent: any,
    groupedEntries: {
      [aa: number]: AccountingLedgerEntry[];
    }
  ) {
    const articlesEle = parent.ele("ARTICLES");

    Object.entries(groupedEntries).forEach(([, entries]) => {
      if (entries.length === 0) return;

      const firstEntry = entries[0];
      const referenceNumber = firstEntry.reference_number || "";
      const articleDate = DateUtil.formatDateToGreek(firstEntry.article_date);

      // Check if this specific article contains both client domestic entries AND has a reference number (invoiced transaction)
      const hasClientDomesticEntry = entries.some(
        (entry) => entry.account_code === LedgerAccounts.CLIENT_DOMESTIC
      );
      const hasReferenceNumber = referenceNumber !== "";
      const custId =
        hasReferenceNumber && hasClientDomesticEntry
          ? ACCOUNTING_CUSTOMER_CONFIG[LedgerAccounts.CLIENT_DOMESTIC].id
          : "";

      const articleEle = articlesEle.ele("ARTICLE", {
        DGRS: "0"
      });

      articleEle.ele("MTYPE").txt("11").up();
      articleEle.ele("ISKEPYO").txt("0").up();
      articleEle.ele("ISAGRYP").txt("").up();
      articleEle.ele("CUSTID").txt(custId).up();
      articleEle.ele("INVOICE").txt(referenceNumber).up();
      articleEle.ele("MDATE").txt(articleDate).up();
      articleEle.ele("REASON").txt(firstEntry.description).up();

      articleEle.ele("KEPYOAMT").txt("").up();
      articleEle.ele("ISBUILD").txt("").up();
      articleEle.ele("INBRCODE").txt("").up();
      articleEle.ele("SUMKEPYOYP").txt("").up();
      articleEle.ele("SUMKEPYONOTYP").txt("").up();
      articleEle.ele("SUMKEPYOFPA").txt("").up();
      articleEle.ele("OTHEREXPEND").txt("").up();
      articleEle.ele("CASHREGISTERID").txt("").up();
      articleEle.ele("HASRETAILID").txt("").up();
      articleEle.ele("CANCELED").txt("").up();
      articleEle.ele("CANCELGROUPID").txt("").up();
      articleEle.ele("ART39BVAT").txt("").up();
      articleEle.ele("UID").txt("").up();

      const detailsEle = articleEle.ele("DETAILS");

      entries.forEach((entry: AccountingLedgerEntry) => {
        const detEle = detailsEle.ele("DETAIL");
        detEle.ele("LCODE").txt(entry.account_code).up();
        const crdb = entry.side === "debit" ? "0" : "1";
        detEle.ele("CRDB").txt(crdb).up();
        detEle.ele("AMOUNT").txt(numberToTwoDecimalsStr(entry.amount, "el")).up();
        detEle.ele("INVOICE").txt(referenceNumber).up();
        detEle.ele("REASON").txt(entry.description).up();
        detEle.ele("ISAGRYP").txt("").up();
        detEle.ele("KEPYOPARTY").txt("").up();
        detEle.up();
      });

      detailsEle.up();
      articleEle.up();
    });

    articlesEle.up();
  }

  private static _addAccountsSection(parent: any, accountCodes: string[]) {
    const accountsEle = parent.ele("ACCOUNTS");

    accountCodes.forEach((accountCode) => {
      const accountEle = accountsEle.ele("ACCOUNT", {
        DGRS: "0"
      });

      accountEle.ele("LCODE").txt(accountCode).up();
      accountEle.ele("LDESC").txt(AccountingReportingService._generateAccountName(accountCode)).up();
      accountEle.ele("ISTRN").txt("1").up();
      accountEle.ele("ACTYPE").txt("9").up();

      accountEle.up();
    });

    accountsEle.up();
  }

  private static _addCustomersSection(parent: any) {
    const customersEle = parent.ele("CUSTOMERS");

    // Add customer for client domestic using configuration
    const customerConfig = ACCOUNTING_CUSTOMER_CONFIG[LedgerAccounts.CLIENT_DOMESTIC];
    const customerEle = customersEle.ele("CUSTOMER");

    customerEle.ele("ID").txt(customerConfig.id).up();
    customerEle.ele("NAME").txt(customerConfig.name).up();
    customerEle.ele("VAT").txt(customerConfig.vat).up();
    customerEle.ele("JOB").txt("").up();
    customerEle.ele("DOYCODE").txt(customerConfig.doyCode).up();
    customerEle.ele("CUSTVAT").txt("").up();
    customerEle.ele("ADDRESS").txt("").up();
    customerEle.ele("ZIP").txt("").up();
    customerEle.ele("CITY").txt("").up();
    customerEle.ele("PHONE1").txt("").up();
    customerEle.ele("PHONE2").txt("").up();
    customerEle.ele("PHONE3").txt("").up();
    customerEle.ele("FAX1").txt("").up();
    customerEle.ele("FAX2").txt("").up();
    customerEle.ele("EMAIL").txt("").up();
    customerEle.ele("ISKEPYO").txt(customerConfig.isKepyo).up();
    customerEle.ele("ISDIMOSIOU").txt("0").up();
    customerEle.ele("ISEA").txt("0").up();
    customerEle.ele("ISAGRYP").txt("0").up();
    customerEle.ele("ACCADDR").txt("").up();
    customerEle.ele("STRADDR").txt("").up();
    customerEle.ele("STRNAME").txt("").up();
    customerEle.ele("BANK1").txt("").up();
    customerEle.ele("BANKACC1").txt("").up();
    customerEle.ele("BANK2").txt("").up();
    customerEle.ele("BANKACC2").txt("").up();
    customerEle.ele("EACOUNTRY").txt("").up();
    customerEle.ele("EAPREFIX").txt("").up();
    customerEle.ele("EAVAT").txt("").up();
    customerEle.ele("IDTYPE").txt("").up();
    customerEle.ele("IDNUMB").txt("").up();

    customerEle.up();
    customersEle.up();
  }

  private static _generateAccountName(accountCode: string): string {
    return ledgerAccountNames[accountCode as keyof typeof ledgerAccountNames] || `Account ${accountCode}`;
  }

  private static _generateFilename(date: Date, prefix: string = ""): string {
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${prefix}accounting_report_${day}-${month}-${year}.xml`;
  }

  private static _generateCsvFilename(date: Date, prefix: string = ""): string {
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${prefix}accounting_report_${day}-${month}-${year}.csv`;
  }

  private static _generateInvoiceCsvFilename(date: Date, prefix: string = ""): string {
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${prefix}invoice_report_${day}-${month}-${year}.csv`;
  }
}
