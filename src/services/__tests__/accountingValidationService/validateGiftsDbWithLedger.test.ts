import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import { AccountingValidationService } from "../../accountingValidationService";
import { buildUser, buildGift, buildReward } from "../../../tests/utils/generateModels";
import { AccountingEventType, LedgerAccounts } from "../../../types/accounting";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { ProviderEnum } from "../../../configs/providersConfig";

describe("AccountingValidationService.validateGiftsDbWithLedger", () => {
  const TODAY = "2025-01-15";
  const FROMDATE = "2025-01-01";

  beforeAll(async () => {
    await connectDb("validateGiftsDbWithLedger");
    await createSqliteDb();
  });

  afterAll(async () => {
    await closeDb();
  });

  beforeEach(async () => {
    // Clear all test data
    await clearSqliteDb();
    await clearDb();
  });

  /* ------------------------------------------------------------------
   * Gift Deposit Settlement Tests
   * ------------------------------------------------------------------ */
  describe("Gift Deposit Settlement", () => {
    it("should validate gift deposit settlement with matching ledger entries", async () => {
      const GIFT_AMOUNT_CENTS = 200_000; // €2,000.00 in cents
      const GIFT_AMOUNT_EUROS = Decimal.div(GIFT_AMOUNT_CENTS, 100).toNumber(); // 2000 euros

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create gift with settled deposit
      const gift = await buildGift({
        targetUserEmail: user.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Create matching ledger entries for deposit settlement
      const description = `${user.id}|${gift.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: GIFT_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: GIFT_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateGiftsDbWithLedger(FROMDATE);

      expect(results).toHaveLength(1);

      const depositResult = results[0];
      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "gifts_deposit",
          isValid: true,
          dbTotalAmount: GIFT_AMOUNT_EUROS,
          ledgerTotalAmount: GIFT_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 2
        })
      );
      expect(depositResult.discrepancies).toBeUndefined();
    });

    it("should detect discrepancies in gift deposit settlement", async () => {
      const GIFT_AMOUNT_CENTS = 150_000; // €1,500.00 in cents
      const GIFT_AMOUNT_EUROS = Decimal.div(GIFT_AMOUNT_CENTS, 100).toNumber();
      const LEDGER_AMOUNT_EUROS = 1200; // Different amount in ledger

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create gift with settled deposit
      const gift = await buildGift({
        targetUserEmail: user.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Create mismatched ledger entries
      const description = `${user.id}|${gift.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateGiftsDbWithLedger(FROMDATE);

      const depositResult = results[0];
      expect(depositResult).toBeDefined();
      expect(depositResult.isValid).toBe(false);
      expect(depositResult.dbTotalAmount).toBe(GIFT_AMOUNT_EUROS);
      expect(depositResult.ledgerTotalAmount).toBe(LEDGER_AMOUNT_EUROS);
      expect(depositResult.difference).toBe(GIFT_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
      expect(depositResult.discrepancies).toHaveLength(1);
      expect(depositResult.discrepancies![0].transactionId).toBe(gift.id);
      expect(depositResult.discrepancies![0].difference).toBe(GIFT_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
    });

    it("should handle multiple gift deposits correctly", async () => {
      const GIFT1_AMOUNT_CENTS = 100_000; // €1,000.00
      const GIFT2_AMOUNT_CENTS = 250_000; // €2,500.00
      const TOTAL_AMOUNT_EUROS = Decimal.div(GIFT1_AMOUNT_CENTS + GIFT2_AMOUNT_CENTS, 100).toNumber();

      // Create two European users
      const user1 = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const user2 = await buildUser({
        residencyCountry: "FR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create two gifts with settled deposits
      const gift1 = await buildGift({
        targetUserEmail: user1.email,
        consideration: { amount: GIFT1_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      const gift2 = await buildGift({
        targetUserEmail: user2.email,
        consideration: { amount: GIFT2_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-2",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Create matching ledger entries for both gifts
      const gift1Amount = Decimal.div(GIFT1_AMOUNT_CENTS, 100).toNumber();
      const gift2Amount = Decimal.div(GIFT2_AMOUNT_CENTS, 100).toNumber();

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Gift 1 entries
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: gift1Amount,
          description: `${user1.id}|${gift1.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift1.id,
          owner_id: user1.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: gift1Amount,
          description: `${user1.id}|${gift1.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift1.id,
          owner_id: user1.id
        },
        // Gift 2 entries
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: gift2Amount,
          description: `${user2.id}|${gift2.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift2.id,
          owner_id: user2.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: gift2Amount,
          description: `${user2.id}|${gift2.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift2.id,
          owner_id: user2.id
        }
      ]);

      const results = await AccountingValidationService.validateGiftsDbWithLedger(FROMDATE);

      const depositResult = results[0];
      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "gifts_deposit",
          isValid: true,
          dbTotalAmount: TOTAL_AMOUNT_EUROS,
          ledgerTotalAmount: TOTAL_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 2,
          ledgerEntryCount: 4
        })
      );
      expect(depositResult.discrepancies).toBeUndefined();
    });

    it("should handle empty gift deposits correctly", async () => {
      const results = await AccountingValidationService.validateGiftsDbWithLedger(FROMDATE);

      expect(results).toHaveLength(1);

      const depositResult = results[0];
      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "gifts_deposit",
          isValid: true,
          dbTotalAmount: 0,
          ledgerTotalAmount: 0,
          difference: 0,
          transactionCount: 0,
          ledgerEntryCount: 0
        })
      );
      expect(depositResult.discrepancies).toBeUndefined();
    });

    it("should filter gifts by currency correctly", async () => {
      const GIFT_AMOUNT_CENTS = 300_000; // €3,000.00 in cents
      const GIFT_AMOUNT_EUROS = Decimal.div(GIFT_AMOUNT_CENTS, 100).toNumber();

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create EUR gift (should be included)
      const eurGift = await buildGift({
        targetUserEmail: user.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-eur",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Create GBP gift (should be excluded)
      await buildGift({
        targetUserEmail: user.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "GBP" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-gbp",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Create ledger entries only for EUR gift
      const description = `${user.id}|${eurGift.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: GIFT_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: eurGift.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: GIFT_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: eurGift.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateGiftsDbWithLedger(FROMDATE);

      const depositResult = results[0];
      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "gifts_deposit",
          isValid: true,
          dbTotalAmount: GIFT_AMOUNT_EUROS,
          ledgerTotalAmount: GIFT_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1, // Only EUR gift counted
          ledgerEntryCount: 2
        })
      );
    });

    it("should filter gifts by date correctly", async () => {
      const GIFT_AMOUNT_CENTS = 250_000; // €2,500.00
      const GIFT_AMOUNT_EUROS = Decimal.div(GIFT_AMOUNT_CENTS, 100).toNumber();

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create gift settled within date range (should be included)
      const recentGift = await buildGift({
        targetUserEmail: user.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-recent",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Create gift settled before date range (should be excluded)
      await buildGift({
        targetUserEmail: user.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-old",
              status: "Settled",
              submittedAt: new Date("2024-12-15") // Before FROMDATE
            }
          }
        }
      });

      // Create ledger entries only for recent gift
      const description = `${user.id}|${recentGift.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: GIFT_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: recentGift.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: GIFT_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: recentGift.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateGiftsDbWithLedger(FROMDATE);

      const depositResult = results[0];
      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "gifts_deposit",
          isValid: true,
          dbTotalAmount: GIFT_AMOUNT_EUROS,
          ledgerTotalAmount: GIFT_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1, // Only recent gift counted
          ledgerEntryCount: 2
        })
      );
    });
  });

  /* ------------------------------------------------------------------
   * Mixed Gifts and Rewards Tests
   * ------------------------------------------------------------------ */
  describe("Mixed Gifts and Rewards", () => {
    it("should handle gifts and rewards together without interference", async () => {
      const GIFT_AMOUNT_CENTS = 200_000; // €2,000.00
      const REWARD_AMOUNT_CENTS = 300_000; // €3,000.00
      const GIFT_AMOUNT_EUROS = Decimal.div(GIFT_AMOUNT_CENTS, 100).toNumber();
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber();

      // Create European users
      const user1 = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const user2 = await buildUser({
        residencyCountry: "FR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create gift and reward
      const gift = await buildGift({
        targetUserEmail: user1.email,
        consideration: { amount: GIFT_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-gift-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      const reward = await buildReward({
        targetUser: user2.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        }
      });

      // Create ledger entries for both
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Gift entries
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: GIFT_AMOUNT_EUROS,
          description: `${user1.id}|${gift.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift.id,
          owner_id: user1.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: GIFT_AMOUNT_EUROS,
          description: `${user1.id}|${gift.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: gift.id,
          owner_id: user1.id
        },
        // Reward entries
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          description: `${user2.id}|${reward.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user2.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          description: `${user2.id}|${reward.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user2.id
        }
      ]);

      // Validate gifts separately
      const giftResults = await AccountingValidationService.validateGiftsDbWithLedger(FROMDATE);
      const giftDepositResult = giftResults[0];
      expect(giftDepositResult).toEqual(
        expect.objectContaining({
          transactionType: "gifts_deposit",
          isValid: true,
          dbTotalAmount: GIFT_AMOUNT_EUROS,
          ledgerTotalAmount: GIFT_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 2
        })
      );

      // Validate rewards separately
      const rewardResults = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);
      const rewardDepositResult = rewardResults.find((r) => r.transactionType === "rewards_deposit");
      expect(rewardDepositResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_deposit",
          isValid: true,
          dbTotalAmount: REWARD_AMOUNT_EUROS,
          ledgerTotalAmount: REWARD_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 2
        })
      );

      // Validate that all validation together works
      const allResults = await AccountingValidationService.validateAllDbWithLedger(FROMDATE);
      expect(allResults).toHaveLength(15); // 14 original + 1 gift

      const giftResultFromAll = allResults.find((r) => r.transactionType === "gifts_deposit");
      expect(giftResultFromAll).toEqual(
        expect.objectContaining({
          transactionType: "gifts_deposit",
          isValid: true,
          dbTotalAmount: GIFT_AMOUNT_EUROS,
          transactionCount: 1
        })
      );

      const rewardResultFromAll = allResults.find((r) => r.transactionType === "rewards_deposit");
      expect(rewardResultFromAll).toEqual(
        expect.objectContaining({
          transactionType: "rewards_deposit",
          isValid: true,
          dbTotalAmount: REWARD_AMOUNT_EUROS,
          transactionCount: 1
        })
      );
    });
  });
});
