import "jest";
import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import { buildUser, buildPortfolio, buildWithdrawalCashTransaction } from "../../../tests/utils/generateModels";
import { AccountingValidationService } from "../../accountingValidationService";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { LedgerAccounts, AccountingEventType } from "../../../types/accounting";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";

describe("AccountingValidationService.validateWithdrawalsDbWithLedger", () => {
  const TODAY = "2025-06-12";

  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));
    await connectDb("validateWithdrawalsDbWithLedger");
    await createSqliteDb();
  });

  afterAll(async () => await closeDb());

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  it("should pass validation when withdrawal amounts match ledger debit entries", async () => {
    const WITHDRAWAL_AMOUNT_CENTS = 800_00; // €800.00 in cents
    const WITHDRAWAL_AMOUNT_EUROS = Decimal.div(WITHDRAWAL_AMOUNT_CENTS, 100).toNumber();

    // Create domestic user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create settled withdrawal
    const withdrawal = await buildWithdrawalCashTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      consideration: { currency: "EUR", amount: WITHDRAWAL_AMOUNT_CENTS },
      providers: { wealthkernel: { id: "wk-wd-1", status: "Settled", settledAt: new Date(TODAY) } }
    });

    // Create corresponding ledger entries (Stage 1: Omnibus → Intermediary)
    const description = `${user.id}|${withdrawal.id}|${AccountingEventType.BANK_TRANSACTION_WITHDRAWAL}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: WITHDRAWAL_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: withdrawal.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_WITHDRAWALS,
        side: "debit",
        amount: WITHDRAWAL_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: withdrawal.id,
        owner_id: user.id
      }
    ]);

    // Validate withdrawals
    const results = await AccountingValidationService.validateWithdrawalsDbWithLedger(TODAY);

    expect(results).toHaveLength(2);
    const [stage1Result] = results;

    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionType).toBe("withdrawals_stage1");
    expect(stage1Result.dbTotalAmount).toBe(WITHDRAWAL_AMOUNT_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(WITHDRAWAL_AMOUNT_EUROS);
    expect(stage1Result.difference).toBe(0);
    expect(stage1Result.transactionCount).toBe(1);
  });

  it("should fail validation when withdrawal amounts don't match ledger entries", async () => {
    const WITHDRAWAL_AMOUNT_CENTS = 800_00; // €800.00 in cents
    const WITHDRAWAL_AMOUNT_EUROS = Decimal.div(WITHDRAWAL_AMOUNT_CENTS, 100).toNumber();
    const LEDGER_AMOUNT_EUROS = 799.95; // Different amount in ledger

    // Create domestic user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create settled withdrawal
    const withdrawal = await buildWithdrawalCashTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      consideration: { currency: "EUR", amount: WITHDRAWAL_AMOUNT_CENTS },
      providers: { wealthkernel: { id: "wk-wd-1", status: "Settled", settledAt: new Date(TODAY) } }
    });

    // Create ledger entry with different amount (Stage 1: Omnibus → Intermediary)
    const description = `${user.id}|${withdrawal.id}|${AccountingEventType.BANK_TRANSACTION_WITHDRAWAL}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: LEDGER_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: withdrawal.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_WITHDRAWALS,
        side: "debit",
        amount: LEDGER_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: withdrawal.id,
        owner_id: user.id
      }
    ]);

    // Validate withdrawals
    const results = await AccountingValidationService.validateWithdrawalsDbWithLedger(TODAY);

    expect(results).toHaveLength(2);
    const [stage1Result] = results;

    expect(stage1Result.isValid).toBe(false);
    expect(stage1Result.transactionType).toBe("withdrawals_stage1");
    expect(stage1Result.dbTotalAmount).toBe(WITHDRAWAL_AMOUNT_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(LEDGER_AMOUNT_EUROS);
    expect(stage1Result.difference).toBe(Decimal.sub(WITHDRAWAL_AMOUNT_EUROS, LEDGER_AMOUNT_EUROS).toNumber());
    expect(stage1Result.discrepancies).toHaveLength(1);
    expect(stage1Result.discrepancies![0]).toEqual({
      transactionId: withdrawal.id,
      dbAmount: WITHDRAWAL_AMOUNT_EUROS,
      ledgerAmount: LEDGER_AMOUNT_EUROS,
      difference: Decimal.sub(WITHDRAWAL_AMOUNT_EUROS, LEDGER_AMOUNT_EUROS).toNumber()
    });
  });

  it("should ignore withdrawals that haven't reached Stage 1 (WealthKernel not settled)", async () => {
    const WITHDRAWAL_AMOUNT_CENTS = 800_00; // €800.00 in cents

    // Create user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create withdrawal that hasn't reached Stage 1 (WealthKernel not settled - should be ignored)
    await buildWithdrawalCashTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      consideration: { currency: "EUR", amount: WITHDRAWAL_AMOUNT_CENTS },
      providers: { wealthkernel: { id: "wk-wd-1", status: "Pending" } }
    });

    // Also create a withdrawal with no providers (also should be ignored)
    await buildWithdrawalCashTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      consideration: { currency: "EUR", amount: WITHDRAWAL_AMOUNT_CENTS }
    });

    // Validate withdrawals
    const results = await AccountingValidationService.validateWithdrawalsDbWithLedger(TODAY);

    expect(results).toHaveLength(2);
    const [stage1Result] = results;

    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionCount).toBe(0);
    expect(stage1Result.dbTotalAmount).toBe(0);
    expect(stage1Result.ledgerTotalAmount).toBe(0);
  });

  it("should include withdrawals that have reached Stage 1 but not Stage 2", async () => {
    const WITHDRAWAL_AMOUNT_CENTS = 800_00; // €800.00 in cents
    const WITHDRAWAL_AMOUNT_EUROS = Decimal.div(WITHDRAWAL_AMOUNT_CENTS, 100).toNumber();

    // Create user
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    // Create withdrawal that has reached Stage 1 (WealthKernel settled) but not Stage 2
    const withdrawal = await buildWithdrawalCashTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      consideration: { currency: "EUR", amount: WITHDRAWAL_AMOUNT_CENTS },
      providers: { wealthkernel: { id: "wk-wd-1", status: "Settled", settledAt: new Date(TODAY) } }
      // Note: No transferWithIntermediary means Stage 2 hasn't started
    });

    // Create Stage 1 ledger entries (Omnibus → Intermediary)
    const description = `${user.id}|${withdrawal.id}|${AccountingEventType.BANK_TRANSACTION_WITHDRAWAL}`;

    await AccountingLedgerStorageService.addValidatedLedgerEntries([
      {
        aa: 1,
        account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
        side: "credit",
        amount: WITHDRAWAL_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: withdrawal.id,
        owner_id: user.id
      },
      {
        aa: 1,
        account_code: LedgerAccounts.INTERMEDIARY_WITHDRAWALS,
        side: "debit",
        amount: WITHDRAWAL_AMOUNT_EUROS,
        article_date: TODAY,
        description,
        document_id: withdrawal.id,
        owner_id: user.id
      }
    ]);

    // Validate withdrawals - should include this withdrawal
    const results = await AccountingValidationService.validateWithdrawalsDbWithLedger(TODAY);

    expect(results).toHaveLength(2);
    const [stage1Result] = results;

    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionCount).toBe(1);
    expect(stage1Result.dbTotalAmount).toBe(WITHDRAWAL_AMOUNT_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(WITHDRAWAL_AMOUNT_EUROS);
  });

  it("should handle validation with no transactions", async () => {
    const results = await AccountingValidationService.validateWithdrawalsDbWithLedger(TODAY);

    expect(results).toHaveLength(2);
    const [stage1Result, stage2Result] = results;

    // All stages should be valid with no transactions
    expect(stage1Result.isValid).toBe(true);
    expect(stage1Result.transactionCount).toBe(0);
    expect(stage1Result.dbTotalAmount).toBe(0);
    expect(stage1Result.ledgerTotalAmount).toBe(0);
    expect(stage1Result.difference).toBe(0);

    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);
  });

  it("should handle validation with no ledger entries", async () => {
    const WITHDRAWAL_AMOUNT_CENTS = 1_000_00;
    const WITHDRAWAL_AMOUNT_EUROS = Decimal.div(WITHDRAWAL_AMOUNT_CENTS, 100).toNumber();

    // Create user and withdrawal without ledger entries
    const user: UserDocument = await buildUser({
      residencyCountry: "GR",
      companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
    });
    const portfolio: PortfolioDocument = await buildPortfolio({ owner: user.id });

    await buildWithdrawalCashTransaction({
      owner: user.id,
      portfolio: portfolio.id,
      consideration: { currency: "EUR", amount: WITHDRAWAL_AMOUNT_CENTS },
      providers: { wealthkernel: { id: "wk-wd-1", status: "Settled", settledAt: new Date(TODAY) } }
    });

    const results = await AccountingValidationService.validateWithdrawalsDbWithLedger(TODAY);

    expect(results).toHaveLength(2);
    const [stage1Result, stage2Result] = results;

    // Stage 1 should fail due to no ledger entries
    expect(stage1Result.isValid).toBe(false);
    expect(stage1Result.transactionCount).toBe(1);
    expect(stage1Result.dbTotalAmount).toBe(WITHDRAWAL_AMOUNT_EUROS);
    expect(stage1Result.ledgerTotalAmount).toBe(0);
    expect(stage1Result.difference).toBe(WITHDRAWAL_AMOUNT_EUROS);

    // Stage 2 should be valid (no transactions)
    expect(stage2Result.isValid).toBe(true);
    expect(stage2Result.transactionCount).toBe(0);
  });
});
