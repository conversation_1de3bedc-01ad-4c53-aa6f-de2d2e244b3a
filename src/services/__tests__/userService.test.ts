import { KycStatusEnum, PlatformType, User, UserDocument, UserTypeEnum } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAccount,
  buildAddress,
  buildAssetTransaction,
  buildBankAccount,
  buildCashbackTransaction,
  buildChargeTransaction,
  buildContentEntry,
  buildDepositCashTransaction,
  buildDividendTransaction,
  buildGift,
  buildInvestmentProduct,
  buildKycOperation,
  buildMandate,
  buildOrder,
  buildParticipant,
  buildPortfolio,
  buildRebalanceTransaction,
  buildReferralCode,
  buildReward,
  buildSavingsDividend,
  buildSavingsProduct,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildSubscription,
  buildTopUpAutomation,
  buildUser,
  buildUserDataRequest,
  buildWealthyhoodDividendTransaction,
  buildWithdrawalCashTransaction
} from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import UserService, { TransactionActivityItemType, UpdateUserOptions } from "../userService";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import DateUtil from "../../utils/dateUtil";
import { CreateParticipantData } from "../../types/requestBody";
import { Participant, ParticipantDocument, TrackingSourceType } from "../../models/Participant";
import { Gift, GiftDocument } from "../../models/Gift";
import { LifetimeEnum, ReferralCode, ReferralCodeDocument } from "../../models/ReferralCode";
import { Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import { Reward, RewardDocument } from "../../models/Reward";
import {
  AssetTransactionDocument,
  CashbackTransaction,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  DisplayTagEnum,
  DividendTransaction,
  RebalanceTransaction,
  RebalanceTransactionDocument,
  TransactionDocument,
  TransactionInvestmentActivityFilterEnum,
  WealthyhoodDividendTransaction,
  WithdrawalCashTransaction
} from "../../models/Transaction";
import { DepositMethodEnum } from "../../types/transactions";
import {
  bannersConfig,
  countriesConfig,
  entitiesConfig,
  investmentUniverseConfig,
  whitelistConfig
} from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../../configs/providersConfig";
import { Account } from "../../models/Account";
import { PortfolioWrapperTypeEnum } from "../brokerageService";
import logger from "../../external-services/loggerService";
import { BankAccountDocument } from "../../models/BankAccount";
import { MandateDocument } from "../../models/Mandate";
import { AutomationDocument } from "../../models/Automation";
import Decimal from "decimal.js";
import { StripeService } from "../../external-services/stripeService";
import { buildContentfulContentEntriesResponse } from "../../tests/utils/generateContentful";
import { Subscription } from "../../models/Subscription";
import { OrderDocument } from "../../models/Order";
import { RiskAssessment, RiskAssessmentDocument } from "../../models/RiskAssessment";
import { RedisClientService } from "../../loaders/redis";
import { MixpanelAccountStatusEnum } from "../../external-services/segmentAnalyticsService";
import { SaltedgeService } from "../../external-services/saltedgeService";
import { AppRating } from "../../models/AppRating";
import { WealthkernelService } from "../../external-services/wealthkernelService";
import { DepositActionEnum } from "../../configs/depositsConfig";
import ContentfulRetrievalService from "../../external-services/contentfulRetrievalService";
import { ContentEntryContentTypeEnum } from "../../models/ContentEntry";
import {
  AppNotificationSettingEnum,
  EmailNotificationSettingEnum,
  NotificationSettings
} from "../../models/NotificationSettings";
import { SumsubService } from "../../external-services/sumsubService";
import { buildApplicant } from "../../tests/utils/generateSumsub";
import { FX_TARGET_SPREADS } from "../../configs/fxSpreadsConfig";

const { BannerEnum, CATEGORY_CONFIG, CategoryEnum } = bannersConfig;
const { ASSET_CONFIG } = investmentUniverseConfig;

describe("UserService", () => {
  beforeAll(async () => await connectDb("UserService"));
  afterAll(async () => await closeDb());

  describe("createOrUpdateUser", () => {
    describe("when user is signing up via mobile platform and a participant exists already for the given email", () => {
      const WLTHD_ID = faker.string.uuid();
      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GA_CLIENT_ID = faker.string.uuid();
      const EMAIL = faker.internet.email();
      let participant: ParticipantDocument;
      let referrer: ParticipantDocument;
      let isNew: boolean;

      beforeAll(async () => {
        jest.resetAllMocks();

        const referralCodes = await ReferralCode.find();
        expect(referralCodes.length).toBe(0);

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        participant = await buildParticipant({
          email: EMAIL,
          participantRole: "BASIC",
          appsflyerId: faker.string.uuid(),
          appInstallInfo: { createdAt: new Date(), platform: "android" }
        });

        const referralData: CreateParticipantData = {
          appInstallInfo: {
            createdAt: new Date(),
            platform: "ios"
          },
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          gaClientId: GA_CLIENT_ID,
          wlthd: WLTHD_ID
        };

        // create referrer
        referrer = await buildParticipant({
          participantRole: "BASIC",
          wlthdId: WLTHD_ID
        });

        const userResponse = await UserService.createOrUpdateUser(EMAIL, userData, referralData);
        isNew = userResponse.isNew;
      });
      afterAll(async () => await clearDb());

      it("should return a flag that the user is new", () => {
        expect(isNew).toBe(true);
      });

      it("should change the last login platform of the user to ios", async () => {
        const newUser = (await User.findOne({})) as UserDocument;
        expect(newUser).toEqual(
          expect.objectContaining({
            lastLoginPlatform: "ios"
          })
        );
      });

      it("should not update the app install info for platform & installation time", async () => {
        const newUser = (await User.findOne({ email: EMAIL.toLowerCase() }).populate(
          "participant"
        )) as UserDocument;
        expect(newUser?.participant?.appInstallInfo?.platform).toEqual("android");
        expect(newUser?.participant?.appInstallInfo?.createdAt).toBeDefined();
      });

      it("should emit a 'referralStatus' and a 'signUp' event", async () => {
        const newUser = (await User.findOne({ email: EMAIL.toLowerCase() })) as UserDocument;

        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.participant.emailSubmitted.eventId,
          expect.anything()
        );
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          1,
          events.user.signUp.eventId,
          expect.objectContaining({
            id: newUser.id,
            participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
          }),
          expect.objectContaining({
            referredStatus: "True"
          })
        );
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          2,
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({
            id: newUser.id,
            participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
          }),
          expect.objectContaining({
            accountStatus: MixpanelAccountStatusEnum.Pending
          })
        );
      });

      it("should update the participant with the referrer data without changing the other details", async () => {
        const updatedParticipant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect((updatedParticipant?.referrer as ParticipantDocument)?.wlthdId).toEqual(referrer.wlthdId);
      });

      it("should not update the app install info & appsflyer id", async () => {
        const updatedParticipant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect(updatedParticipant?.anonymousId).toEqual(participant?.anonymousId);
        expect(updatedParticipant?.appsflyerId).toEqual(participant.appsflyerId);
        expect(updatedParticipant?.gaClientId).toEqual(participant?.gaClientId);
      });

      it("should create a new referral code for the user", async () => {
        const createdUser = (await User.findOne({ email: EMAIL })) as UserDocument;
        const referralCodes = await ReferralCode.find({ owner: createdUser.id });

        expect(referralCodes.length).toBe(1);
        expect(referralCodes[0]).toMatchObject(
          expect.objectContaining({
            active: true,
            lifetime: LifetimeEnum.EXPIRING
          })
        );
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });

      it("should create notification settings", async () => {
        const user = await User.findOne({ email: EMAIL });
        const notificationSettings = await NotificationSettings.find({ owner: user.id });
        expect(notificationSettings).toHaveLength(1);
        expect(notificationSettings[0].toObject()).toMatchObject({
          owner: user._id,
          app: {
            deviceNotificationsEnabled: true,
            settings: new Map([
              [AppNotificationSettingEnum.TRANSACTIONAL, true],
              [AppNotificationSettingEnum.LEARNING_GUIDE, true],
              [AppNotificationSettingEnum.ANALYST_INSIGHT, true],
              [AppNotificationSettingEnum.QUICK_TAKE, true],
              [AppNotificationSettingEnum.DAILY_RECAP, true],
              [AppNotificationSettingEnum.WEEKLY_REVIEW, true],
              [AppNotificationSettingEnum.PROMOTIONAL, true]
            ])
          },
          email: {
            settings: new Map([
              [EmailNotificationSettingEnum.TRANSACTIONAL, true],
              [EmailNotificationSettingEnum.WEALTHYBITES, true],
              [EmailNotificationSettingEnum.PROMOTIONAL, true]
            ])
          }
        });
      });
    });

    describe("when user is signing up via mobile platform and a participant exists already for the given appsflyer id but has no email", () => {
      const WLTHD_ID = "wlthd-id";
      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GA_CLIENT_ID = faker.string.uuid();
      const EMAIL = faker.internet.email();
      let referrer: ParticipantDocument;
      let isNew: boolean;

      beforeAll(async () => {
        jest.resetAllMocks();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        await buildParticipant({
          email: undefined,
          appsflyerId: APPSFLYER_ID,
          participantRole: "BASIC",
          appInstallInfo: { createdAt: new Date(), platform: "android" }
        });

        const referralData: CreateParticipantData = {
          appInstallInfo: {
            createdAt: new Date(),
            platform: "ios"
          },
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          gaClientId: GA_CLIENT_ID,
          wlthd: WLTHD_ID
        };

        // create referrer
        referrer = await buildParticipant({
          participantRole: "BASIC",
          wlthdId: WLTHD_ID
        });

        const userResponse = await UserService.createOrUpdateUser(EMAIL, userData, referralData);
        isNew = userResponse.isNew;
      });
      afterAll(async () => await clearDb());

      it("should return a flag that the user is new", () => {
        expect(isNew).toBe(true);
      });

      it("should change the last login platform of the user to ios", async () => {
        const newUser = (await User.findOne({})) as UserDocument;
        expect(newUser).toEqual(
          expect.objectContaining({
            lastLoginPlatform: "ios"
          })
        );
      });

      it("should emit a 'signUp' user event", async () => {
        const newUser = (await User.findOne({ email: EMAIL.toLowerCase() })) as UserDocument;

        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          1,
          events.participant.emailSubmitted.eventId,
          expect.anything()
        );
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          2,
          events.user.signUp.eventId,
          expect.objectContaining({
            id: newUser.id,
            participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
          }),
          expect.objectContaining({
            referredStatus: "True"
          })
        );
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          3,
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({
            id: newUser.id,
            participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
          }),
          expect.objectContaining({
            accountStatus: MixpanelAccountStatusEnum.Pending
          })
        );
      });

      it("should update the participant doc with the attribution params and the email", async () => {
        const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
        expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
        expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
        expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WLTHD_ID);
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user is signing up via mobile platform and no participant exists", () => {
      const WLTHD_ID = "wlthd-id";
      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GOOGLE_CAMPAIGN = "Search-Investing";
      const PAGE_USER_LANDED = "Page-User-Landed";
      const GA_CLIENT_ID = faker.string.uuid();
      const GCLID = faker.string.uuid();
      const EMAIL = faker.internet.email();
      let referrer: ParticipantDocument;
      let isNew: boolean;

      beforeAll(async () => {
        jest.resetAllMocks();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referralData: CreateParticipantData = {
          appInstallInfo: {
            createdAt: new Date(),
            platform: "ios"
          },
          anonymousId: ANONYMOUS_ID,
          pageUserLanded: PAGE_USER_LANDED,
          appsflyerId: APPSFLYER_ID,
          googleAdsMetadata: {
            campaign: GOOGLE_CAMPAIGN,
            gclid: GCLID
          },
          trackingSource: "google",
          gaClientId: GA_CLIENT_ID,
          wlthd: WLTHD_ID
        };

        // create referrer
        referrer = await buildParticipant({ participantRole: "BASIC", wlthdId: WLTHD_ID });

        const userResponse = await UserService.createOrUpdateUser(EMAIL, userData, referralData);
        isNew = userResponse.isNew;
      });
      afterAll(async () => await clearDb());

      it("should return a flag that the user is new", () => {
        expect(isNew).toBe(true);
      });

      it("should change the last login platform of the user to ios", async () => {
        const newUser = (await User.findOne({})) as UserDocument;
        expect(newUser).toEqual(
          expect.objectContaining({
            lastLoginPlatform: "ios"
          })
        );
      });

      it("should store the app install info for platform & installation time", async () => {
        const newUser = (await User.findOne({}).populate("participant")) as UserDocument;
        expect(newUser?.participant?.appInstallInfo?.platform).toEqual("ios");
        expect(newUser?.participant?.appInstallInfo?.createdAt).toBeDefined();
      });

      it("should emit an 'emailSubmitted' and a 'signUp' user event", async () => {
        const newUser = (await User.findOne({})) as UserDocument;

        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          1,
          events.participant.emailSubmitted.eventId,
          expect.objectContaining({ email: EMAIL.toLowerCase() })
        );
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          2,
          events.user.signUp.eventId,
          expect.objectContaining({
            id: newUser.id,
            participant: expect.objectContaining({
              referrer: expect.objectContaining({ email: referrer.email?.toLowerCase() })
            })
          }),
          expect.objectContaining({
            referredStatus: "True"
          })
        );
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          3,
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({
            id: newUser.id,
            participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
          }),
          expect.objectContaining({
            accountStatus: MixpanelAccountStatusEnum.Pending
          })
        );
      });

      it("should create a new participant with attribution params & google ads metadata stored", async () => {
        const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WLTHD_ID);
        expect(participant).toMatchObject(
          expect.objectContaining({
            anonymousId: ANONYMOUS_ID,
            appsflyerId: APPSFLYER_ID,
            gaClientId: GA_CLIENT_ID,
            pageUserLanded: PAGE_USER_LANDED,
            trackingSource: "google",
            metadata: expect.objectContaining({
              googleAds: expect.objectContaining({
                campaign: GOOGLE_CAMPAIGN,
                gclid: GCLID
              })
            })
          })
        );
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user is signing up via mobile platform, no participant exists and user has gift", () => {
      const WLTHD_ID = "wlthd-id";
      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const PAGE_USER_LANDED = "Page-User-Landed";
      const GOOGLE_CAMPAIGN = "Search-Investing";
      const GA_CLIENT_ID = faker.string.uuid();
      const GCLID = faker.string.uuid();
      const EMAIL = faker.internet.email().toLowerCase();

      let referrer: ParticipantDocument;
      let gift: GiftDocument;
      let isNew: boolean;

      beforeAll(async () => {
        jest.resetAllMocks();

        const anotherUser = await buildUser();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referralData: CreateParticipantData = {
          appInstallInfo: {
            createdAt: new Date(),
            platform: "ios"
          },
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          pageUserLanded: PAGE_USER_LANDED,
          googleAdsMetadata: {
            campaign: GOOGLE_CAMPAIGN,
            gclid: GCLID
          },
          trackingSource: "google",
          gaClientId: GA_CLIENT_ID,
          wlthd: WLTHD_ID
        };

        // create referrer
        referrer = await buildParticipant({ participantRole: "BASIC", wlthdId: WLTHD_ID });

        gift = await buildGift({
          gifter: anotherUser._id,
          targetUserEmail: EMAIL
        });

        const userResponse = await UserService.createOrUpdateUser(EMAIL, userData, referralData);
        isNew = userResponse.isNew;
      });
      afterAll(async () => await clearDb());

      it("should return a flag that the user is new", () => {
        expect(isNew).toBe(true);
      });

      it("should change the last login platform of the user to ios", async () => {
        const newUser = (await User.findOne({ email: EMAIL })) as UserDocument;
        expect(newUser).toEqual(
          expect.objectContaining({
            lastLoginPlatform: "ios"
          })
        );
      });

      it("should store the app install info for platform & installation time", async () => {
        const newUser = (await User.findOne({ email: EMAIL }).populate("participant")) as UserDocument;
        expect(newUser?.participant?.appInstallInfo?.platform).toEqual("ios");
        expect(newUser?.participant?.appInstallInfo?.createdAt).toBeDefined();
      });

      it("should emit an 'emailSubmitted' and a 'signUp' user event", async () => {
        const newUser = (await User.findOne({ email: EMAIL })) as UserDocument;

        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          1,
          events.participant.emailSubmitted.eventId,
          expect.objectContaining({ email: EMAIL.toLowerCase() })
        );
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          2,
          events.user.signUp.eventId,
          expect.objectContaining({
            id: newUser.id,
            participant: expect.objectContaining({
              referrer: expect.objectContaining({ email: referrer.email?.toLowerCase() })
            })
          }),
          {
            referredStatus: "True",
            gift: expect.objectContaining({ id: gift.id })
          }
        );
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          3,
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({
            id: newUser.id,
            participant: expect.objectContaining({ referrer: expect.objectContaining({ email: referrer.email }) })
          }),
          expect.objectContaining({
            accountStatus: MixpanelAccountStatusEnum.Pending
          })
        );
      });

      it("should create a new participant with attribution params & google ads metadata stored", async () => {
        const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WLTHD_ID);
        expect(participant).toMatchObject(
          expect.objectContaining({
            anonymousId: ANONYMOUS_ID,
            appsflyerId: APPSFLYER_ID,
            gaClientId: GA_CLIENT_ID,
            pageUserLanded: PAGE_USER_LANDED,
            trackingSource: "google",
            metadata: expect.objectContaining({
              googleAds: expect.objectContaining({
                campaign: GOOGLE_CAMPAIGN,
                gclid: GCLID
              })
            })
          })
        );
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("it should set the user's viewedReferralCodeScreen to true", async () => {
        const newUser = (await User.findOne({ email: EMAIL })) as UserDocument;

        expect(newUser?.viewedReferralCodeScreen).toEqual(true);
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user is logging in via mobile platform for the first time", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildParticipant({ email: user.email, appInstallInfo: undefined, appsflyerId: undefined });

        const userData = {
          auth0: { id: `email|${user.email}` },
          email: user.email,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referrerData = {
          appInstallInfo: {
            platform: "ios" as "ios" | "android",
            createdAt: new Date()
          },
          appsflyerId: "appsflyer-id"
        };

        await UserService.createOrUpdateUser(user.email, userData, referrerData);
      });
      afterAll(async () => await clearDb());

      it("should change the last login platform of the user to ios", async () => {
        const updatedUser = await User.findOne({});
        expect(updatedUser).toEqual(
          expect.objectContaining({
            lastLoginPlatform: "ios"
          })
        );
      });

      it("should store the app install info for platform & installation time", async () => {
        const newUser = (await User.findOne({}).populate("participant")) as UserDocument;
        expect(newUser?.participant?.appInstallInfo?.platform).toEqual("ios");
        expect(newUser?.participant?.appInstallInfo?.createdAt).toBeDefined();
      });

      it("should emit a 'login' user event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.logIn.eventId,
          expect.objectContaining({ id: user.id, lastLoginPlatform: "ios" }),
          expect.objectContaining({ justDownloadedApp: true })
        );
      });
    });

    describe("when user is logging in via a different mobile platform than before", () => {
      const INSTALL_TIME = new Date();
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          lastLoginPlatform: "android"
        });
        await buildParticipant({
          email: user.email,
          appInstallInfo: {
            platform: "android",
            createdAt: INSTALL_TIME
          }
        });
        const userData = {
          auth0: { id: `email|${user.email}` },
          email: user.email,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };

        await UserService.createOrUpdateUser(user.email, userData);
      });
      afterAll(async () => await clearDb());

      it("should change the last login platform of the user to ios", async () => {
        const updatedUser = await User.findOne({});
        expect(updatedUser).toEqual(
          expect.objectContaining({
            lastLoginPlatform: "ios"
          })
        );
      });

      it("should not update the app install info for platform & installation time", async () => {
        const newUser = (await User.findOne({}).populate("participant")) as UserDocument;
        expect(newUser?.participant?.appInstallInfo?.platform).toEqual("android");
        expect(newUser?.participant?.appInstallInfo?.createdAt).toEqual(INSTALL_TIME);
      });

      it("should emit a 'login' user event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.logIn.eventId,
          expect.objectContaining({ id: user.id, lastLoginPlatform: "ios" }),
          expect.objectContaining({ justDownloadedApp: false })
        );
      });
    });

    describe("when a user is logging in and the corresponding participant does not exist", () => {
      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GA_CLIENT_ID = faker.string.uuid();

      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(eventEmitter, "emit");
        user = await buildUser();
        const userData = {
          auth0: { id: `email|${user.email}` },
          email: user.email,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referralData: CreateParticipantData = {
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          gaClientId: GA_CLIENT_ID
        };
        await user.populate("participant");
        expect(user.participant?.email).toBeUndefined();

        await UserService.createOrUpdateUser(user.email, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should create the missing participant document", async () => {
        const participant = await Participant.findOne({ email: user.email });
        expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
        expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
        expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
      });

      it("should not emit a emailSubmitted event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.participant.emailSubmitted.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when a user is logging in and the corresponding participant does not have an appsflyer id", () => {
      const APPSFLYER_ID = faker.string.uuid();

      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildParticipant({ email: user.email });

        const userData = {
          auth0: { id: `email|${user.email}` },
          email: user.email,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referralData: CreateParticipantData = {
          appsflyerId: APPSFLYER_ID,
          appInstallInfo: {
            createdAt: new Date(),
            platform: "ios"
          }
        };
        await user.populate("participant");
        expect(user.participant.email).toEqual(user.email);
        expect(user.participant.appsflyerId).toBeUndefined();
        expect(user.participant.appInstallInfo).toMatchObject({});

        await UserService.createOrUpdateUser(user.email, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should update the participant document with the appsflyerId and the app install info", async () => {
        const participant = await Participant.findOne({ email: user.email });
        expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
        expect(participant?.appInstallInfo).toMatchObject(expect.objectContaining({ platform: "ios" }));
      });
    });

    describe("when user signs up with the free reward campaign wlthd id", () => {
      const REFERRER_WLTHD_ID = "wlthd-id";
      const WH_FREE_ETF_WLTHD_ID = "yfset5ax"; // this is our wlthd id for users signing up through the free etf page

      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GA_CLIENT_ID = faker.string.uuid();
      const EMAIL = faker.internet.email();

      let wealthyhoodParticipant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referralData: CreateParticipantData = {
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          gaClientId: GA_CLIENT_ID,
          wlthd: WH_FREE_ETF_WLTHD_ID
        };

        // create referrer
        await buildParticipant({ participantRole: "BASIC", wlthdId: REFERRER_WLTHD_ID });
        // wealthyhood participant
        wealthyhoodParticipant = await buildParticipant({
          participantRole: "BASIC",
          wlthdId: WH_FREE_ETF_WLTHD_ID
        });

        await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should create a new participant referred by the wealthyhood participant", async () => {
        const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
        expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
        expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
        expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WH_FREE_ETF_WLTHD_ID);

        const user = await User.findOne({ email: EMAIL });
        expect(user?.referredByEmail).toEqual(wealthyhoodParticipant.email);
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user signs up referred from submission technology", () => {
      const REFERRER_WLTHD_ID = "wlthd-id";
      const WH_FREE_ETF_WLTHD_ID = "yfset5ax"; // this is our wlthd id for users signing up through the free etf page

      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GA_CLIENT_ID = faker.string.uuid();
      const SUBMISSION_TECH_ID = faker.string.uuid();
      const EMAIL = faker.internet.email();

      let wealthyhoodParticipant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referralData: CreateParticipantData = {
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          gaClientId: GA_CLIENT_ID,
          wlthd: WH_FREE_ETF_WLTHD_ID,
          submissionTechClickId: SUBMISSION_TECH_ID
        };

        // create referrer
        await buildParticipant({ participantRole: "BASIC", wlthdId: REFERRER_WLTHD_ID });
        // wealthyhood participant
        wealthyhoodParticipant = await buildParticipant({
          participantRole: "BASIC",
          wlthdId: WH_FREE_ETF_WLTHD_ID
        });

        await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should create a new participant referred by the wealthyhood participant", async () => {
        const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
        expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
        expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
        expect(participant?.metadata?.submissionTech?.clickId).toEqual(SUBMISSION_TECH_ID);
        expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WH_FREE_ETF_WLTHD_ID);

        const user = await User.findOne({ email: EMAIL });
        expect(user?.referredByEmail).toEqual(wealthyhoodParticipant.email);
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user signs up with both a wlthd id & finance ads id", () => {
      const REFERRER_WLTHD_ID = "wlthd-id";
      const WH_FREE_ETF_WLTHD_ID = "yfset5ax"; // this is our wlthd id for users signing up through the free etf page
      const FINANCE_ADS_WLTHD_ID = "rz81ojjz";

      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const FINANCER_ADS_ID = faker.string.uuid();
      const GA_CLIENT_ID = faker.string.uuid();
      const EMAIL = faker.internet.email();

      let userParticipant: ParticipantDocument;
      let referralCode: ReferralCodeDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        referralCode = await buildReferralCode();
        await referralCode.populate("owner");

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referralData: CreateParticipantData = {
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          financeAdsSessionId: FINANCER_ADS_ID,
          gaClientId: GA_CLIENT_ID,
          wlthd: referralCode.code
        };

        // create referrer
        userParticipant = await buildParticipant({
          email: (referralCode.owner as UserDocument).email,
          participantRole: "BASIC",
          wlthdId: REFERRER_WLTHD_ID
        });
        // wealthyhood participant
        await buildParticipant({
          participantRole: "BASIC",
          wlthdId: WH_FREE_ETF_WLTHD_ID
        });
        // finance ads participant
        await buildParticipant({
          participantRole: "BASIC",
          wlthdId: FINANCE_ADS_WLTHD_ID
        });

        await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should create a new participant referred by the participant that corresponds to the wlthd id param", async () => {
        const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
        expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
        expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
        expect((participant?.referrer as ParticipantDocument)?.email).toEqual(
          (referralCode.owner as UserDocument)?.email
        );

        const user = await User.findOne({ email: EMAIL });
        expect(user?.referredByEmail).toEqual(userParticipant.email);
      });

      it("should make inactive the old referral code and generate a new one for the referrer", async () => {
        const oldReferralCode = await ReferralCode.findOne({ _id: referralCode.id });
        expect(oldReferralCode?.active).toBe(false);

        const updatedUser = await User.findOne({ _id: (oldReferralCode as ReferralCodeDocument).owner }).populate(
          "oneTimeReferralCode"
        );
        expect((updatedUser?.oneTimeReferralCode as ReferralCodeDocument).active).toBe(true);
        expect((updatedUser?.oneTimeReferralCode as ReferralCodeDocument).code).not.toBe(referralCode.code);
      });

      it("should create a new referral code for the user", async () => {
        const createdUser = (await User.findOne({ email: EMAIL })) as UserDocument;
        const referralCodes = await ReferralCode.find({ owner: createdUser.id });

        expect(referralCodes.length).toBe(1);
        expect(referralCodes[0]).toMatchObject(
          expect.objectContaining({
            active: true,
            lifetime: LifetimeEnum.EXPIRING
          })
        );
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user signs up with a finance ads id & a wlthd id that corresponds to the free reward campaign", () => {
      const REFERRER_WLTHD_ID = "wlthd-id";
      const WH_FREE_ETF_WLTHD_ID = "yfset5ax"; // this is our wlthd id for users signing up through the free etf page
      const FINANCE_ADS_WLTHD_ID = "rz81ojjz";

      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const FINANCER_ADS_ID = faker.string.uuid();
      const GA_CLIENT_ID = faker.string.uuid();
      const EMAIL = faker.internet.email();

      let financeAdsParticipant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referralData: CreateParticipantData = {
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          financeAdsSessionId: FINANCER_ADS_ID,
          gaClientId: GA_CLIENT_ID,
          // important => we need to
          wlthd: WH_FREE_ETF_WLTHD_ID
        };

        // user referrer
        await buildParticipant({ participantRole: "BASIC", wlthdId: REFERRER_WLTHD_ID });
        // wealthyhood participant
        await buildParticipant({
          participantRole: "BASIC",
          wlthdId: WH_FREE_ETF_WLTHD_ID
        });
        // finance ads participant
        financeAdsParticipant = await buildParticipant({
          participantRole: "BASIC",
          wlthdId: FINANCE_ADS_WLTHD_ID
        });

        await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should create a new participant referred by the participant that corresponds to the finance ads participant", async () => {
        const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect(participant?.anonymousId).toEqual(ANONYMOUS_ID);
        expect(participant?.appsflyerId).toEqual(APPSFLYER_ID);
        expect(participant?.gaClientId).toEqual(GA_CLIENT_ID);
        expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(FINANCE_ADS_WLTHD_ID);

        const user = await User.findOne({ email: EMAIL });
        expect(user?.referredByEmail).toEqual(financeAdsParticipant.email);
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user signs up and a participant exists already for the given email and has google ads metadata", () => {
      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GA_CLIENT_ID = faker.string.uuid();
      const EMAIL = faker.internet.email();
      const GOOGLE_CAMPAIGN = "Search-Investing";
      const GCLID = faker.string.uuid();
      const TRACKING_SOURCE: TrackingSourceType = "google";

      beforeAll(async () => {
        jest.resetAllMocks();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        await buildParticipant({
          email: EMAIL,
          participantRole: "BASIC",
          appsflyerId: faker.string.uuid(),
          appInstallInfo: { createdAt: new Date(), platform: "android" },
          trackingSource: TRACKING_SOURCE,
          metadata: {
            googleAds: {
              gclid: GCLID,
              campaign: GOOGLE_CAMPAIGN
            }
          }
        });

        const referralData: CreateParticipantData = {
          appInstallInfo: {
            createdAt: new Date(),
            platform: "ios"
          },
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          gaClientId: GA_CLIENT_ID
        };

        await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should not update the google ads metadata", async () => {
        const updatedParticipant = await Participant.findOne({ email: EMAIL });
        expect(updatedParticipant).toMatchObject(
          expect.objectContaining({
            trackingSource: TRACKING_SOURCE,
            metadata: expect.objectContaining({
              googleAds: expect.objectContaining({
                gclid: GCLID,
                campaign: GOOGLE_CAMPAIGN
              })
            })
          })
        );
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user signs up and a participant exists already for the given email but has no google ads metadata", () => {
      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GA_CLIENT_ID = faker.string.uuid();
      const EMAIL = faker.internet.email();
      const TRACKING_SOURCE: TrackingSourceType = "google";
      const GCLID = faker.string.uuid();
      const GOOGLE_CAMPAIGN = "Search-Investing";

      beforeAll(async () => {
        jest.resetAllMocks();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        await buildParticipant({
          email: EMAIL,
          participantRole: "BASIC",
          appsflyerId: faker.string.uuid(),
          appInstallInfo: { createdAt: new Date(), platform: "android" },
          metadata: {}
        });

        const referralData: CreateParticipantData = {
          appInstallInfo: {
            createdAt: new Date(),
            platform: "ios"
          },
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          gaClientId: GA_CLIENT_ID,
          trackingSource: TRACKING_SOURCE,
          googleAdsMetadata: {
            gclid: GCLID,
            campaign: GOOGLE_CAMPAIGN
          }
        };

        await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should store the google ads metadata", async () => {
        const updatedParticipant = await Participant.findOne({ email: EMAIL });
        expect(updatedParticipant).toMatchObject(
          expect.objectContaining({
            trackingSource: TRACKING_SOURCE,
            metadata: expect.objectContaining({
              googleAds: expect.objectContaining({
                gclid: GCLID,
                campaign: GOOGLE_CAMPAIGN
              })
            })
          })
        );
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user is signing up via mobile platform with upper case 'wlthdId' that matches a participant's 'wlthdId'", () => {
      const WLTHD_ID = "wlthd-id";
      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GOOGLE_CAMPAIGN = "Search-Investing";
      const GA_CLIENT_ID = faker.string.uuid();
      const GCLID = faker.string.uuid();
      const EMAIL = faker.internet.email();

      beforeAll(async () => {
        jest.resetAllMocks();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };
        const referralData: CreateParticipantData = {
          appInstallInfo: {
            createdAt: new Date(),
            platform: "ios"
          },
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          googleAdsMetadata: {
            campaign: GOOGLE_CAMPAIGN,
            gclid: GCLID
          },
          trackingSource: "google",
          gaClientId: GA_CLIENT_ID,
          wlthd: WLTHD_ID.toUpperCase()
        };

        // create referrer, with lowercase 'wlthId'
        await buildParticipant({ participantRole: "BASIC", wlthdId: WLTHD_ID });

        // call createOrUpdateUser with uppercase 'wlthId'
        await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should create a new participant that is referred by the participant that matches 'wlthdId'", async () => {
        const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(WLTHD_ID);
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });

    describe("when user is signing up via mobile platform with upper case 'wlthdId' that matches a referral code", () => {
      const WLTHD_ID = "wlthd-id";
      const ANONYMOUS_ID = faker.string.uuid();
      const APPSFLYER_ID = faker.string.uuid();
      const GOOGLE_CAMPAIGN = "Search-Investing";
      const GA_CLIENT_ID = faker.string.uuid();
      const GCLID = faker.string.uuid();
      const EMAIL = faker.internet.email();
      let referrer: ParticipantDocument;
      let referrerUser: UserDocument;
      let referralCode: ReferralCodeDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        const userData = {
          auth0: { id: `email|${EMAIL}` },
          email: EMAIL,
          emailVerified: true,
          lastLogin: new Date(),
          role: [UserTypeEnum.INVESTOR],
          lastLoginPlatform: "ios" as PlatformType
        };

        // create referrer, with lowercase 'wlthId'
        const referrerEmail = faker.internet.email();
        referrer = await buildParticipant({ participantRole: "BASIC", email: referrerEmail });
        referrerUser = await buildUser({ email: referrerEmail });
        referralCode = await buildReferralCode({ owner: referrerUser.id, lifetime: "expiring", code: WLTHD_ID });

        const referralData: CreateParticipantData = {
          appInstallInfo: {
            createdAt: new Date(),
            platform: "ios"
          },
          anonymousId: ANONYMOUS_ID,
          appsflyerId: APPSFLYER_ID,
          googleAdsMetadata: {
            campaign: GOOGLE_CAMPAIGN,
            gclid: GCLID
          },
          trackingSource: "google",
          gaClientId: GA_CLIENT_ID,
          wlthd: WLTHD_ID
        };

        // call createOrUpdateUser with uppercase 'wlthId'
        await UserService.createOrUpdateUser(EMAIL, userData, referralData);
      });
      afterAll(async () => await clearDb());

      it("should create a new participant that is referred by the owner (participant) of the referralCode", async () => {
        const participant = await Participant.findOne({ email: EMAIL }).populate("referrer");
        expect((participant?.referrer as ParticipantDocument)?.wlthdId).toEqual(referrer.wlthdId);
      });

      it("should deactivate the referralCode", async () => {
        const updatedReferralCode = await ReferralCode.findById(referralCode.id);

        expect(updatedReferralCode?.active).toEqual(false);
      });

      it("should create an account", async () => {
        const user = await User.findOne({ email: EMAIL });
        const accounts = await Account.find({});
        expect(accounts.length).toEqual(1);
        expect(accounts[0]).toMatchObject({
          wrapperType: PortfolioWrapperTypeEnum.GIA,
          name: "General Investment Account",
          owner: user._id
        });
      });

      it("should create a real portfolio", async () => {
        const user = await User.findOne({ email: EMAIL });
        const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
        expect(portfolios.length).toEqual(1);
        expect(portfolios[0].toObject()).toMatchObject({
          owner: user._id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
      });
    });
  });

  describe("updateUser", () => {
    beforeAll(() => {
      jest.spyOn(eventEmitter, "emit");
    });

    describe("when user submits their tax details for the first time", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          taxResidency: undefined
        });

        const userData: Partial<UserDocument> = {
          taxResidency: {
            countryCode: "GB",
            proofType: "NINO",

            value: "test"
          },
          isUKTaxResident: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should emit a taxDetailsSubmission event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.taxDetailsSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when user has already submitted their tax details for the first time", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          taxResidency: {
            countryCode: "GB",
            proofType: "NINO",
            value: "test2"
          },
          isUKTaxResident: true
        });

        const userData: Partial<UserDocument> = {
          taxResidency: {
            countryCode: "GB",
            proofType: "NINO",
            value: "test2"
          },
          isUKTaxResident: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should NOT emit a taxDetailsSubmission event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
      });
    });

    describe("when user submits their employment info for the first time", () => {
      let user: UserDocument;
      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          employmentInfo: undefined
        });

        const userData: Partial<UserDocument> = {
          employmentInfo: {
            annualIncome: { amount: 5000, currency: "GBP" },
            incomeRangeId: "1",
            employmentStatus: "Retired",
            sourcesOfWealth: ["Gift"],
            industry: "AgricultureForestryAndFishing"
          }
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should emit a employmentInfoSubmission event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.employmentInfoSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should emit a personalDetailsSubmission event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.personalDetailsSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when user has already submitted their employment info for the first time", () => {
      let user: UserDocument;
      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          employmentInfo: {
            annualIncome: { amount: 5000, currency: "GBP" },
            incomeRangeId: "1",
            employmentStatus: "Retired",
            sourcesOfWealth: ["Gift"],
            industry: "AgricultureForestryAndFishing"
          },
          taxResidencySubmitted: true
        });

        const userData: Partial<UserDocument> = {
          employmentInfo: {
            annualIncome: { amount: 5000, currency: "GBP" },
            incomeRangeId: "1",
            employmentStatus: "Retired",
            sourcesOfWealth: ["Gift"],
            industry: "AgricultureForestryAndFishing"
          }
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should NOT emit a employmentInfoSubmission event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.user.employmentInfoSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when user has accepted terms for the first time", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          hasAcceptedTerms: false,
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          }
        });

        const userData: Partial<UserDocument> = {
          hasAcceptedTerms: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should emit a termsAccepted event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.termsAccepted.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should store a timestamp in the submittedRequiredInfoAt field", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(new Date(updatedUser.submittedRequiredInfoAt as Date).getTime()).toBeGreaterThan(
          DateUtil.getDateOfMinutesAgo(1).getTime()
        );
      });

      it("should store a W-8BEN form completion timestamp", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.w8BenForm).toEqual({
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          completedAt: updatedUser.submittedRequiredInfoAt
        });
      });
    });

    describe("when user has already accepted terms for the first time", () => {
      let user: UserDocument;
      const SUBMITTED_TIME = new Date();

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: SUBMITTED_TIME,
          hasAcceptedTerms: true
        });

        const userData: Partial<UserDocument> = {
          hasAcceptedTerms: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should NOT emit a termsAccepted event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.user.termsAccepted.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should not update the submittedRequiredInfoAt field", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(new Date(updatedUser.submittedRequiredInfoAt as Date).getTime()).toBe(SUBMITTED_TIME.getTime());
      });
    });

    describe("when user has accepted terms for the first time and is KYC failed", () => {
      let user: UserDocument;
      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          hasAcceptedTerms: false,
          kycStatus: KycStatusEnum.FAILED
        });

        const userData: Partial<UserDocument> = {
          hasAcceptedTerms: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should emit a termsAccepted event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.termsAccepted.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should emit a verification failed event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.verificationFailure.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should store a timestamp in the submittedRequiredInfoAt field", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(new Date(updatedUser.submittedRequiredInfoAt as Date).getTime()).toBeGreaterThan(
          DateUtil.getDateOfMinutesAgo(1).getTime()
        );
      });
    });

    describe("when user submits only some of the required tax details", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined
        });

        const userData: Partial<UserDocument> = {
          taxResidency: {} as any,
          isUKTaxResident: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("no taxDetailsSubmission event should be submitted", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.user.taxDetailsSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("the submittedRequiredInfoAt field should be empty", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.submittedRequiredInfoAt).not.toBeDefined();
      });
    });

    describe("when user submits empty string for 'referredByEmail' field to override previous value", () => {
      let user: UserDocument;
      const REFERRER_EMAIL = faker.internet.email().toLowerCase();
      const UPDATED_REFERRER_EMAIL = "";

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          referredByEmail: REFERRER_EMAIL
        });

        const userData: Partial<UserDocument> = {
          referredByEmail: UPDATED_REFERRER_EMAIL
        };

        const options: UpdateUserOptions = {
          fieldsToDelete: {
            referredByEmail: true
          }
        };

        await UserService.updateUser(user.id, userData, options);
      });

      it("the 'referredByEmail' field should be undefined", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).not.toBeDefined();
      });
    });

    describe("when user submits empty string for undefined 'referredByEmail' field", () => {
      let user: UserDocument;
      const UPDATED_REFERRER_EMAIL = "";

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();

        const userData: Partial<UserDocument> = {
          referredByEmail: UPDATED_REFERRER_EMAIL
        };

        const options: UpdateUserOptions = {
          fieldsToDelete: {
            referredByEmail: true
          }
        };

        await UserService.updateUser(user.id, userData, options);
      });

      it("the 'referredByEmail' field should be undefined", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).not.toBeDefined();
      });
    });

    describe("when user submits residency country", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ residencyCountry: undefined });
        const account = await buildAccount({
          activeProviders: undefined,
          owner: user._id
        });
        await buildPortfolio({
          activeProviders: undefined,
          mode: PortfolioModeEnum.REAL,
          owner: user._id,
          account: account._id
        });

        expect(user.residencyCountry).toBeUndefined();

        await UserService.updateUser(user.id, { residencyCountry: "GB" });
      });
      afterEach(async () => await clearDb());

      it("should update user's residency country", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.residencyCountry).toEqual("GB");
      });

      it("should add active providers to the account", async () => {
        const account = await Account.findOne({ owner: user._id });
        expect(account?.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
      });

      it("should add active providers to the real portfolio", async () => {
        const realPortfolio = await Portfolio.findOne({ owner: user._id, mode: PortfolioModeEnum.REAL });
        expect(realPortfolio?.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
      });
    });

    describe("when user submits a different residency country", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ residencyCountry: "GR" });
        const account = await buildAccount({
          activeProviders: undefined,
          owner: user._id
        });
        await buildPortfolio({
          activeProviders: undefined,
          mode: PortfolioModeEnum.REAL,
          owner: user._id,
          account: account._id
        });

        await UserService.updateUser(user.id, { residencyCountry: "GB" });
      });
      afterEach(async () => await clearDb());

      it("should update user's residency country", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.residencyCountry).toEqual("GB");
      });

      it("should add active providers to the account", async () => {
        const account = await Account.findOne({ owner: user._id });
        expect(account?.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
      });

      it("should add active providers to the real portfolio", async () => {
        const realPortfolio = await Portfolio.findOne({ owner: user._id, mode: PortfolioModeEnum.REAL });
        expect(realPortfolio?.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
      });
    });

    describe("when user submits residency country and without any portfolios or account", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ residencyCountry: undefined });

        expect(user.residencyCountry).toBeUndefined();

        await UserService.updateUser(user.id, { residencyCountry: "GB" });
      });
      afterEach(async () => await clearDb());

      it("should update user's residency country", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.residencyCountry).toEqual("GB");
      });

      it("should log an error regarding missing account", async () => {
        expect(logger.error).toHaveBeenCalledWith(`Could not add active providers to account for ${user.id}`, {
          module: "UserService",
          method: "updateUser",
          data: {
            userId: user.id
          }
        });
      });

      it("should log an error regarding missing account", async () => {
        expect(logger.error).toHaveBeenCalledWith(
          `Could not add active providers to real portfolio for ${user.id}`,
          {
            module: "UserService",
            method: "updateUser",
            data: {
              userId: user.id
            }
          }
        );
      });
    });
  });

  describe("setReferrerByCode", () => {
    describe("when user is not referred and the code is expiring", () => {
      const USER_EMAIL = faker.internet.email().toLowerCase();
      const REFERRER_EMAIL = faker.internet.email().toLowerCase();

      let referrer: UserDocument;
      let user: UserDocument;
      let usedReferralCode: ReferralCodeDocument;
      let referrerParticipant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ email: USER_EMAIL });
        await buildParticipant({ email: USER_EMAIL });

        // Build referrer
        referrer = await buildUser({ email: REFERRER_EMAIL });
        referrerParticipant = await buildParticipant({ email: REFERRER_EMAIL });

        usedReferralCode = await buildReferralCode({ owner: referrer.id, lifetime: LifetimeEnum.EXPIRING });

        await UserService.setReferrerByCode(user.id, `${usedReferralCode.code.toUpperCase()} `);
      });

      it("should update the user's referredByEmail property", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).toBe(REFERRER_EMAIL);
      });

      it("should update the user's participant document with the referrer", async () => {
        const updatedParticipant = await Participant.findOne({ email: USER_EMAIL }).populate("referrer");
        expect((updatedParticipant?.referrer as ParticipantDocument)?.wlthdId).toEqual(
          referrerParticipant.wlthdId
        );
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.referralCodeSubmission.eventId,
          expect.objectContaining({ id: user.id }),
          { referredStatus: "True" }
        );
      });

      it("should generate a new referral code", async () => {
        const referralCode = (await ReferralCode.findOne({
          owner: referrer.id,
          active: true
        })) as ReferralCodeDocument;
        expect(referralCode).not.toBeNull();
        expect(referralCode.code).not.toBe(usedReferralCode.code);
      });
    });

    describe("when user is not referred and the code is non-expiring", () => {
      const USER_EMAIL = faker.internet.email().toLowerCase();
      const REFERRER_EMAIL = faker.internet.email().toLowerCase();

      let referrer: UserDocument;
      let user: UserDocument;
      let usedReferralCode: ReferralCodeDocument;
      let referrerParticipant: ParticipantDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ email: USER_EMAIL });
        await buildParticipant({ email: USER_EMAIL });

        // Build referrer
        referrer = await buildUser({ email: REFERRER_EMAIL });
        referrerParticipant = await buildParticipant({ email: REFERRER_EMAIL });

        usedReferralCode = await buildReferralCode({ owner: referrer.id, lifetime: LifetimeEnum.NON_EXPIRING });

        await UserService.setReferrerByCode(user.id, `${usedReferralCode.code.toUpperCase()} `);
      });

      it("should update the user's referredByEmail property", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).toBe(REFERRER_EMAIL);
      });

      it("should update the user's participant document with the referrer", async () => {
        const updatedParticipant = await Participant.findOne({ email: USER_EMAIL }).populate("referrer");
        expect((updatedParticipant?.referrer as ParticipantDocument)?.wlthdId).toEqual(
          referrerParticipant.wlthdId
        );
      });

      it("should emit an event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.referralCodeSubmission.eventId,
          expect.objectContaining({ id: user.id }),
          { referredStatus: "True" }
        );
      });

      it("should not generate a new referral code", async () => {
        const referralCodes = (await ReferralCode.find({
          owner: referrer.id,
          active: true
        })) as ReferralCodeDocument[];
        expect(referralCodes.length).toBe(1);

        const referralCode = referralCodes[0];
        expect(referralCode).not.toBeNull();
        expect(referralCode.code).toBe(usedReferralCode.code);
      });
    });

    // In the case that a user uses whitelisted promo code, we don't consider it as a referral reward,
    // so we don't update the referredByEmail property.
    // We follow a different flow to hand out rewards to whitelisted users
    describe("when user uses a whitelisted promo code for EU", () => {
      const USER_EMAIL = faker.internet.email().toLowerCase();

      let user: UserDocument;
      let euWhitelistPromoCode = faker.string.nanoid();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(whitelistConfig, "isPromoCodeWhitelisted").mockReturnValue(true);

        user = await buildUser({ email: USER_EMAIL });

        await UserService.setReferrerByCode(user.id, `${euWhitelistPromoCode} `);
      });

      it("should NOT update the user's referredByEmail property", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).toBe(undefined);
      });

      it("should update the user's usedWhitelistCode property", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.usedWhitelistCode).toBe(true);
      });
    });
  });

  describe("canUnlockFreeShare", () => {
    describe("when user is not referred", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred but has signed up more than 10 days ago", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          createdAt: new Date("2022-07-29T11:00:00Z"),
          referredByEmail: faker.internet.email()
        });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred, has signed up today but has >= £100 investments", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ referredByEmail: faker.internet.email() });
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          originalInvestmentAmount: 10000,
          consideration: {
            amount: 10000,
            currency: "GBP"
          }
        });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred, has signed up today, has <100 investments but already has a reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ referredByEmail: faker.internet.email() });
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          originalInvestmentAmount: 9000,
          consideration: {
            amount: 9000,
            currency: "GBP"
          }
        });

        await buildReward({ targetUser: user.id });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });

    describe("when user is referred, has signed up today, has < £100 investments, has a portfolio allocation and does not have a reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ referredByEmail: faker.internet.email() });
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: [
            {
              percentage: 100,
              assetCommonId: "equities_us"
            }
          ]
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          originalInvestmentAmount: 9000,
          consideration: {
            amount: 9000,
            currency: "GBP"
          }
        });
      });

      it("should return true", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(true);
      });
    });

    describe("when user is referred, has signed up today, has < £100 investments, does NOT have a portfolio allocation and does not have a reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({ referredByEmail: faker.internet.email() });
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          initialHoldingsAllocation: []
        });
        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          originalInvestmentAmount: 9000,
          consideration: {
            amount: 9000,
            currency: "GBP"
          }
        });
      });

      it("should return false", async () => {
        const canUnlockFreeShare = await UserService.canUnlockFreeShare(user);
        expect(canUnlockFreeShare).toBe(false);
      });
    });
  });

  describe("canReceiveCashback", () => {
    describe("when user has no subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return false", async () => {
        const canReceiveCashback = await UserService.canReceiveCashback(user);
        expect(canReceiveCashback).toBe(false);
      });
    });

    describe("when user has an inactive paid_low subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildSubscription({ owner: user.id, active: false, price: "paid_low_monthly" });
      });
      afterAll(async () => await clearDb());

      it("should return false", async () => {
        const canReceiveCashback = await UserService.canReceiveCashback(user);
        expect(canReceiveCashback).toBe(false);
      });
    });

    describe("when user has an active paid_low subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildSubscription({ owner: user.id, active: true, price: "paid_low_monthly" });
      });
      afterAll(async () => await clearDb());

      it("should return true", async () => {
        const canReceiveCashback = await UserService.canReceiveCashback(user);
        expect(canReceiveCashback).toBe(true);
      });
    });

    describe("when user has an active lifetime subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildSubscription({ owner: user.id, active: true, price: "paid_mid_lifetime_blackfriday_2023" });
      });
      afterAll(async () => await clearDb());

      it("should return true", async () => {
        const canReceiveCashback = await UserService.canReceiveCashback(user);
        expect(canReceiveCashback).toBe(true);
      });
    });
  });

  describe("createAllStripeCustomers", () => {
    describe("when user has not submitted all required info", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(StripeService.Instance, "createCustomer");

        await buildUser({ submittedRequiredInfoAt: undefined });

        await UserService.createAllStripeCustomers();
      });
      afterAll(async () => await clearDb());

      it("should not call Stripe", async () => {
        expect(StripeService.Instance.createCustomer).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible and hasn't been created in Stripe", () => {
      let user: UserDocument;

      const stripeId = "CU-123";

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(StripeService.Instance, "createCustomer").mockResolvedValue({ id: stripeId });

        user = await buildUser();

        await UserService.createAllStripeCustomers();
      });
      afterAll(async () => await clearDb());

      it("should call Stripe", async () => {
        expect(StripeService.Instance.createCustomer).toHaveBeenCalledTimes(1);
        expect(StripeService.Instance.createCustomer).toHaveBeenCalledWith({
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          metadata: {
            wealthyhoodId: user.id
          }
        });
      });

      it("should update the user document with the Stripe ID", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.providers?.stripe?.id).toEqual(stripeId);
      });
    });

    describe("when user has already been created in Stripe", () => {
      const stripeId = "CU-123";

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(StripeService.Instance, "createCustomer");

        await buildUser({ providers: { stripe: { id: stripeId } } });

        await UserService.createAllStripeCustomers();
      });
      afterAll(async () => await clearDb());

      it("should not call Stripe", async () => {
        expect(StripeService.Instance.createCustomer).not.toHaveBeenCalled();
      });
    });
  });

  describe("createAllSaltedgeLeads", () => {
    describe("when user has not submitted all required info", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(SaltedgeService.Instance, "createLead");

        await buildUser({ submittedRequiredInfoAt: undefined, activeProviders: [ProviderEnum.SALTEDGE] });

        await UserService.createAllSaltedgeLeads();
      });
      afterAll(async () => await clearDb());

      it("should not call Saltedge", async () => {
        expect(SaltedgeService.Instance.createLead).not.toHaveBeenCalled();
      });
    });

    describe("when user has already been created in Saltedge", () => {
      const saltedgeId = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(SaltedgeService.Instance, "createLead");

        await buildUser({ providers: { saltedge: { id: saltedgeId } }, activeProviders: [ProviderEnum.SALTEDGE] });

        await UserService.createAllSaltedgeLeads();
      });
      afterAll(async () => await clearDb());

      it("should not call Saltedge", async () => {
        expect(SaltedgeService.Instance.createLead).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible and hasn't been created in Saltedge", () => {
      let user: UserDocument;

      const saltedgeId = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(SaltedgeService.Instance, "createLead").mockResolvedValue({
          data: {
            customer_id: saltedgeId
          }
        });

        user = await buildUser({ activeProviders: [ProviderEnum.SALTEDGE] });

        await UserService.createAllSaltedgeLeads();
      });
      afterAll(async () => await clearDb());

      it("should call Saltedge", async () => {
        expect(SaltedgeService.Instance.createLead).toHaveBeenCalledTimes(1);
        expect(SaltedgeService.Instance.createLead).toHaveBeenCalledWith({
          fullName: user.fullName,
          email: user.email,
          identifier: user.id
        });
      });

      it("should update the user document with the Saltedge ID", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.providers?.saltedge?.id).toEqual(saltedgeId);
      });
    });
  });

  describe("createAllWkW8BenForms", () => {
    describe("when user is KYC pending", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm");

        await buildUser({ kycStatus: KycStatusEnum.PENDING });

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is KYC failed", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm");

        await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.FAILED,
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          }
        });

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is KYC passed but submitted all required info less than 10 minutes ago", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm");

        await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          }
        });

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is deleted", () => {
      let user: UserDocument;

      const W_8BEN_FORM_ID = faker.string.uuid();
      const ACCEPTED_TERMS_COMPLETION_DATE = new Date();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm").mockResolvedValue({
          id: W_8BEN_FORM_ID
        });

        user = await buildUser({
          email: `deleted_${faker.internet.email()}`,
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(20),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: ACCEPTED_TERMS_COMPLETION_DATE
          }
        });

        await Promise.all([
          buildAddress({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid()
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          }),
          buildUserDataRequest({
            owner: user.id,
            requestType: "disassociation"
          })
        ]);

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible but already has a W-8BEN form created", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm");

        const user = await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(20),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: new Date(),
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending"
              }
            }
          }
        });

        await Promise.all([
          buildAddress({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid()
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          })
        ]);

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible and does not have W-8BEN form created", () => {
      let user: UserDocument;

      const W_8BEN_FORM_ID = faker.string.uuid();
      const ACCEPTED_TERMS_COMPLETION_DATE = new Date();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm").mockResolvedValue({
          id: W_8BEN_FORM_ID
        });

        user = await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(20),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: ACCEPTED_TERMS_COMPLETION_DATE
          }
        });

        await Promise.all([
          buildAddress({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid()
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          })
        ]);

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).toHaveBeenCalledTimes(1);
        expect(WealthkernelService.UKInstance.createW8BenForm).toHaveBeenCalledWith({
          partyId: user.providers.wealthkernel.id
        });
      });

      it("should update the user document with the W8_BEN form", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser).toEqual(
          expect.objectContaining({
            w8BenForm: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              completedAt: new Date(ACCEPTED_TERMS_COMPLETION_DATE),
              providers: expect.objectContaining({
                wealthkernel: {
                  id: W_8BEN_FORM_ID,
                  status: "Pending"
                }
              })
            }
          })
        );
      });
    });
  });

  describe("completeAllWkW8BenForms", () => {
    describe("when user does not have a W-8BEN form created", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "completeW8BenForm");

        await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5)
        });

        await UserService.completeAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.completeW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible but already has a W-8BEN form completed", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "completeW8BenForm");

        await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: new Date(),
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Completed"
              }
            }
          }
        });

        await UserService.completeAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.completeW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible and does not have W-8BEN form completed", () => {
      let user: UserDocument;

      const W_8BEN_FORM_ID = faker.string.uuid();
      const ACCEPTED_TERMS_COMPLETION_DATE = new Date();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "completeW8BenForm").mockResolvedValue({
          id: W_8BEN_FORM_ID
        });

        user = await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(15),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: ACCEPTED_TERMS_COMPLETION_DATE,
            providers: {
              wealthkernel: {
                id: W_8BEN_FORM_ID,
                status: "Pending"
              }
            }
          }
        });

        await UserService.completeAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.completeW8BenForm).toHaveBeenCalledTimes(1);
        expect(WealthkernelService.UKInstance.completeW8BenForm).toHaveBeenCalledWith(W_8BEN_FORM_ID, {
          completedAt: ACCEPTED_TERMS_COMPLETION_DATE.toISOString()
        });
      });

      it("should update the user document with the W8_BEN form", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser).toEqual(
          expect.objectContaining({
            w8BenForm: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              completedAt: ACCEPTED_TERMS_COMPLETION_DATE,
              providers: expect.objectContaining({
                wealthkernel: {
                  id: W_8BEN_FORM_ID,
                  status: "Completed"
                }
              })
            }
          })
        );
      });
    });
  });

  describe("getPrompts", () => {
    // This the minimum number of banner prompts this method will return
    // This should be updated if the default minimum has changed
    const MINIMUM_BANNER_PROMPTS_VERIFIED_USERS = 4;
    let user: UserDocument;

    const EUR_ONE_DAY_YIELD = 3.5;
    const GBP_ONE_DAY_YIELD = 4.7;

    const ASSET_COMMON_IDS_CONFIG: {
      assetId: investmentUniverseConfig.AssetType;
      quantity: number;
      price: number;
      percentage: number;
    }[] = [
      { assetId: "equities_china", quantity: 2, price: 10, percentage: 30 },
      { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
      { assetId: "government_bonds_us", quantity: 2, price: 10, percentage: 40 }
    ];

    beforeEach(async () => {
      user = await buildUser({ kycStatus: KycStatusEnum.PASSED });

      await buildPortfolio({
        owner: user.id,
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });

      await Promise.all([
        buildSavingsProduct(true, { commonId: "mmf_dist_eur" }, { oneDayYield: EUR_ONE_DAY_YIELD }),
        buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { oneDayYield: GBP_ONE_DAY_YIELD })
      ]);
    });
    afterEach(async () => {
      jest.clearAllMocks();
      await clearDb();
    });

    it("should return app rating prompt modal", async () => {
      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });
      await buildTopUpAutomation({ owner: user._id, active: true });

      const prompts = await UserService.getPrompts(user);

      const appRating = await AppRating.findOne({ owner: user._id });

      expect(prompts).toMatchObject({
        modalPrompts: [
          {
            order: 0,
            modalType: "AppRatingPrompt",
            data: {
              appRatingId: appRating.id
            }
          }
        ]
      });
    });

    it("should return all modal prompts by order", async () => {
      const reward = await buildReward({
        hasViewedAppModal: false,
        targetUser: user.id
      });
      const gift = await buildGift({
        hasViewedAppModal: false,
        targetUserEmail: user.email
      });
      const wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
        hasViewedAppModal: false,
        owner: user.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled",
              submittedAt: new Date()
            }
          }
        }
      });
      await buildTopUpAutomation({ owner: user._id, active: true });

      const prompts = await UserService.getPrompts(user, "modal");

      const appRating = await AppRating.findOne({ owner: user._id });

      expect(prompts).toEqual({
        modalPrompts: [
          {
            order: 0,
            modalType: "AppRatingPrompt",
            data: {
              appRatingId: appRating.id
            }
          },
          {
            order: 1,
            modalType: "Reward",
            data: { rewards: expect.arrayContaining([expect.objectContaining({ _id: reward._id })]) }
          },
          {
            order: 2,
            modalType: "Gift",
            data: { gifts: expect.arrayContaining([expect.objectContaining({ _id: gift._id })]) }
          },
          {
            order: 3,
            modalType: "WealthyhoodDividend",
            data: {
              wealthyhoodDividends: expect.arrayContaining([
                expect.objectContaining({ _id: wealthyhoodDividend._id })
              ])
            }
          }
        ]
      });
    });

    it("should return all available modal prompts by order (gift + dividend)", async () => {
      const gift = await buildGift({
        hasViewedAppModal: false,
        targetUserEmail: user.email
      });
      const wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
        hasViewedAppModal: false,
        owner: user.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        }
      });

      const prompts = await UserService.getPrompts(user, "modal");

      expect(prompts).toEqual({
        modalPrompts: [
          {
            order: 0,
            modalType: "Gift",
            data: { gifts: expect.arrayContaining([expect.objectContaining({ _id: gift._id })]) }
          },
          {
            order: 1,
            modalType: "WealthyhoodDividend",
            data: {
              wealthyhoodDividends: expect.arrayContaining([
                expect.objectContaining({ _id: wealthyhoodDividend._id })
              ])
            }
          }
        ]
      });
    });

    it("should NOT return RedeemGift modal if the user has not set a target allocation & has no holdings", async () => {
      const userWhoCanNotRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWhoCanNotRedeemGift.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: []
      });

      await buildGift({
        hasViewedAppModal: false,
        targetUserEmail: userWhoCanNotRedeemGift.email
      });

      const prompts = await UserService.getPrompts(userWhoCanNotRedeemGift, "modal");

      expect(prompts).toEqual({
        modalPrompts: []
      });
    });

    it("should return RedeemGift banner prompt if available", async () => {
      const userWhoCanRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWhoCanRedeemGift.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });

      await buildSubscription({
        owner: userWhoCanRedeemGift.id,
        price: "free_monthly"
      });
      const userWhoCanNotRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWhoCanNotRedeemGift.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: userWhoCanNotRedeemGift.id,
        price: "free_monthly"
      });

      const gift = await buildGift({
        hasViewedAppModal: false,
        targetUserEmail: userWhoCanRedeemGift.email
      });

      let prompts = await UserService.getPrompts(userWhoCanNotRedeemGift, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);

      prompts = await UserService.getPrompts(userWhoCanRedeemGift, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.RedeemGift,
            order: 0,
            data: expect.objectContaining({
              gifts: expect.arrayContaining([expect.objectContaining({ _id: gift._id })]),
              title: expect.anything()
            })
          })
        ])
      });
    });

    it("should NOT return RedeemGift banner prompt if gift is used", async () => {
      const userWhoCanNotRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWhoCanNotRedeemGift.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });

      await buildSubscription({
        owner: userWhoCanNotRedeemGift.id,
        price: "free_monthly"
      });

      const gift = await buildGift({
        targetUserEmail: userWhoCanNotRedeemGift.email
      });

      // create asset transaction that uses the gift
      await buildAssetTransaction({
        owner: userWhoCanNotRedeemGift.id,
        pendingGift: gift.id
      });

      const prompts = await UserService.getPrompts(userWhoCanNotRedeemGift, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);
    });

    it("should NOT return RedeemGift banner prompt if the user has not set a target allocation & has no holdings", async () => {
      const userWhoCanNotRedeemGift = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWhoCanNotRedeemGift.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: []
      });

      await buildSubscription({
        owner: userWhoCanNotRedeemGift.id,
        price: "free_monthly"
      });

      const gift = await buildGift({
        targetUserEmail: userWhoCanNotRedeemGift.email
      });

      // create asset transaction that uses the gift
      await buildAssetTransaction({
        owner: userWhoCanNotRedeemGift.id,
        pendingGift: gift.id
      });

      const prompts = await UserService.getPrompts(userWhoCanNotRedeemGift, "banner");

      expect(prompts).not.toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.RedeemGift,
            order: expect.anything()
          })
        ])
      });
    });

    it("should return SendGift banner prompt if available", async () => {
      const TODAY = new Date("2023-09-18T11:00:00Z");

      Date.now = jest.fn(() => TODAY.valueOf());

      const userWhoCannotSendGift = await buildUser({
        portfolioConversionStatus: "completed",
        canSendGiftUntil: DateUtil.getDateOfDaysAgo(TODAY, 1),
        kycStatus: KycStatusEnum.PASSED
      });
      await buildPortfolio({
        owner: userWhoCannotSendGift.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: userWhoCannotSendGift.id,
        price: "free_monthly"
      });

      let prompts = await UserService.getPrompts(userWhoCannotSendGift, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);

      const userWhoCanSendGift = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        canSendGiftUntil: DateUtil.getDateAfterNdays(TODAY, 1)
      });
      await buildPortfolio({
        owner: userWhoCanSendGift.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: userWhoCanSendGift.id,
        price: "free_monthly"
      });

      prompts = await UserService.getPrompts(userWhoCanSendGift, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.SendGift,
            order: 0,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });

    it("should return PendingReward banner prompt if available", async () => {
      const userWithPendingReward = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWithPendingReward.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: userWithPendingReward.id,
        price: "free_monthly"
      });
      const userWithoutPendingReward = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWithoutPendingReward.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });

      await buildSubscription({
        owner: userWithoutPendingReward.id,
        price: "free_monthly"
      });

      const reward = await buildReward({
        hasViewedAppModal: false,
        targetUser: userWithPendingReward.id
      });

      let prompts = await UserService.getPrompts(userWithoutPendingReward, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);

      prompts = await UserService.getPrompts(userWithPendingReward, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.PendingReward,
            order: 0,
            data: expect.objectContaining({
              rewards: expect.arrayContaining([expect.objectContaining({ _id: reward._id })]),
              title: "1 pending reward!"
            })
          })
        ])
      });
    });

    it("should return PendingReward banner prompt with correct rewardCount if multiple rewards are pending", async () => {
      const userWithPendingReward = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      const [, , rewardA, rewardB] = await Promise.all([
        await buildPortfolio({
          owner: userWithPendingReward.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        }),
        buildSubscription({
          owner: userWithPendingReward.id,
          price: "free_monthly"
        }),
        buildReward({
          hasViewedAppModal: false,
          targetUser: userWithPendingReward.id
        }),
        buildReward({
          hasViewedAppModal: false,
          targetUser: userWithPendingReward.id
        })
      ]);
      const userWithoutPendingReward = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWithoutPendingReward.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: userWithoutPendingReward.id,
        price: "free_monthly"
      });

      let prompts = await UserService.getPrompts(userWithoutPendingReward, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);

      prompts = await UserService.getPrompts(userWithPendingReward, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.PendingReward,
            order: 0,
            data: expect.objectContaining({
              rewards: expect.arrayContaining([
                expect.objectContaining({ _id: rewardA._id }),
                expect.objectContaining({ _id: rewardB._id })
              ]),
              title: "2 pending rewards!"
            })
          })
        ])
      });
    });

    it("should NOT return PendingReward banner prompt if a reward has `accepted: false`", async () => {
      const userWithPendingReward = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWithPendingReward.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: userWithPendingReward.id,
        price: "free_monthly"
      });
      const userWithoutPendingReward = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWithoutPendingReward.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: userWithoutPendingReward.id,
        price: "free_monthly"
      });

      await buildReward({
        hasViewedAppModal: false,
        targetUser: userWithPendingReward.id,
        accepted: false
      });

      let prompts = await UserService.getPrompts(userWithoutPendingReward, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);

      prompts = await UserService.getPrompts(userWithPendingReward, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);
      expect(prompts).not.toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.PendingReward,
            order: 0
          })
        ])
      });
    });

    it("should return UnlockFreeShare banner prompt if available", async () => {
      const userWhoCanUnlockFreeShare = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: userWhoCanUnlockFreeShare.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: userWhoCanUnlockFreeShare.id,
        price: "free_monthly"
      });

      const userWhoCanNotUnlockFreeShare = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildSubscription({
        owner: userWhoCanNotUnlockFreeShare.id,
        price: "free_monthly"
      });
      await buildPortfolio({
        owner: userWhoCanNotUnlockFreeShare.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });

      jest.spyOn(UserService, "canUnlockFreeShare").mockImplementation(async (user: UserDocument) => {
        return user.id === userWhoCanUnlockFreeShare.id;
      });

      let prompts = await UserService.getPrompts(userWhoCanNotUnlockFreeShare, "banner");
      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);

      prompts = await UserService.getPrompts(userWhoCanUnlockFreeShare, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.UnlockFreeShare,
            order: 0,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });

    it("should always return LatestAnalysis banner if a content entry exists", async () => {
      const LATEST_ANALYST_INSIGHT_TITLE = "Bond managers";
      const BANNER_IMAGE_URL = "some-image-url";

      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      const contentEntry = await buildContentEntry({ contentType: ContentEntryContentTypeEnum.ANALYSIS });
      const contentfulResponse = buildContentfulContentEntriesResponse([
        {
          title: LATEST_ANALYST_INSIGHT_TITLE,
          id: contentEntry?.providers?.contentful?.id,
          analystInsightType: contentEntry.contentType,
          bannerImageURL: BANNER_IMAGE_URL,
          createdAt: contentEntry.createdAt
        }
      ]);

      jest
        .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntry")
        .mockImplementation(async (id: string): Promise<any> => {
          const matchingContentEntry = contentfulResponse.items.find(
            (contentfulEntry) => id === contentfulEntry.sys.id
          );
          return Promise.resolve(matchingContentEntry);
        });

      const prompts = await UserService.getPrompts(user, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.LatestAnalysis,
            data: {
              title: LATEST_ANALYST_INSIGHT_TITLE,
              imageURL: BANNER_IMAGE_URL,
              analystInsightId: contentEntry.id
            },
            order: 1
          })
        ])
      });
    });

    it("should always return LatestQuickTake banner if a content entry exists", async () => {
      const LATEST_ANALYST_INSIGHT_TITLE = "Bond managers";
      const BANNER_IMAGE_URL = "some-image-url";

      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      const contentEntry = await buildContentEntry({ contentType: ContentEntryContentTypeEnum.QUICK_TAKE });
      const contentfulResponse = buildContentfulContentEntriesResponse([
        {
          title: LATEST_ANALYST_INSIGHT_TITLE,
          id: contentEntry?.providers?.contentful?.id,
          analystInsightType: contentEntry.contentType,
          bannerImageURL: BANNER_IMAGE_URL,
          createdAt: contentEntry.createdAt
        }
      ]);

      jest
        .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntry")
        .mockImplementation(async (id: string): Promise<any> => {
          const matchingContentEntry = contentfulResponse.items.find(
            (contentfulEntry) => id === contentfulEntry.sys.id
          );
          return Promise.resolve(matchingContentEntry);
        });

      const prompts = await UserService.getPrompts(user, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.LatestQuickTake,
            data: {
              title: LATEST_ANALYST_INSIGHT_TITLE,
              imageURL: BANNER_IMAGE_URL,
              analystInsightId: contentEntry.id
            },
            order: 1
          })
        ])
      });
    });

    it("should always return LatestWeeklyReview banner if a content entry exists", async () => {
      const LATEST_ANALYST_INSIGHT_TITLE = "Bond managers";
      const BANNER_IMAGE_URL = "some-image-url";

      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      const contentEntry = await buildContentEntry({ contentType: ContentEntryContentTypeEnum.WEEKLY_REVIEW });
      const contentfulResponse = buildContentfulContentEntriesResponse([
        {
          title: LATEST_ANALYST_INSIGHT_TITLE,
          id: contentEntry?.providers?.contentful?.id,
          analystInsightType: contentEntry.contentType,
          bannerImageURL: BANNER_IMAGE_URL,
          createdAt: contentEntry.createdAt
        }
      ]);

      jest
        .spyOn(ContentfulRetrievalService.LearnHubInstance, "getEntry")
        .mockImplementation(async (id: string): Promise<any> => {
          const matchingContentEntry = contentfulResponse.items.find(
            (contentfulEntry) => id === contentfulEntry.sys.id
          );
          return Promise.resolve(matchingContentEntry);
        });

      const prompts = await UserService.getPrompts(user, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.LatestWeeklyReview,
            data: {
              title: LATEST_ANALYST_INSIGHT_TITLE,
              imageURL: BANNER_IMAGE_URL,
              analystInsightId: contentEntry.id
            },
            order: 1
          })
        ])
      });
    });

    it("should return different banner from investment promotion category based on date", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });

      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });

      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      const TODAY = new Date("2023-09-18T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      let prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.WhySetUpMonthlyInvestment,
            order: 0,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });

      const sampleDuration = CATEGORY_CONFIG[CategoryEnum.INVESTMENT_PROMOTION].options?.sampleDuration as number;

      Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
      prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.WhatIsDCA,
            order: 0,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });

      Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, 2 * sampleDuration).valueOf());
      prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnFreeShares,
            order: 0,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });

    it("should not return repeating investment related banners from investment promotion category when user has an active repeating investment", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });

      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]])
      });

      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      await buildTopUpAutomation({
        owner: user.id,
        category: "TopUpAutomation",
        active: true
      });

      const TODAY = new Date("2023-09-18T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      let prompts = await UserService.getPrompts(user, "banner");
      expect(prompts.bannerPrompts).toEqual(
        expect.arrayContaining([
          expect.not.objectContaining({
            bannerId: BannerEnum.WhySetUpMonthlyInvestment
          }),
          expect.not.objectContaining({
            bannerId: BannerEnum.WhatIsDCA
          }),
          expect.not.objectContaining({
            bannerId: BannerEnum.EarnFreeShares
          })
        ])
      );

      const sampleDuration = CATEGORY_CONFIG[CategoryEnum.INVESTMENT_PROMOTION].options?.sampleDuration as number;

      Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
      prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toMatchObject({
        bannerPrompts: expect.arrayContaining([
          expect.not.objectContaining({
            bannerId: BannerEnum.WhySetUpMonthlyInvestment
          }),
          expect.not.objectContaining({
            bannerId: BannerEnum.WhatIsDCA
          }),
          expect.not.objectContaining({
            bannerId: BannerEnum.EarnFreeShares
          })
        ])
      });

      Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, 2 * sampleDuration).valueOf());
      prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toMatchObject({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnFreeShares,
            order: 0,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });

    it("should not return repeating investment related banners from investment promotion category when user has not set up target portfolio", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });

      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]])
      });

      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      const TODAY = new Date("2023-09-18T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      const prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).not.toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.WhySetUpMonthlyInvestment,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });

    it("should return different banner from plan promotion category based on date", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });

      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });

      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      const TODAY = new Date("2023-09-18T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      let prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnBonusDividendPlus,
            order: 1,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });

      const sampleDuration = CATEGORY_CONFIG[CategoryEnum.PLAN_PROMOTION].options?.sampleDuration as number;

      Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
      prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnCashbackPlus,
            order: 1,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });

      Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, 2 * sampleDuration).valueOf());
      prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.PlanUpgradePlus,
            order: 1,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });

    it("should not return plan promotion banners if user has paid_mid plan", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });

      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });

      await buildSubscription({
        owner: user.id,
        price: "paid_mid_monthly"
      });

      const TODAY = new Date("2023-09-18T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      let prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).not.toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnBonusDividendPlus,
            order: 2,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });

      const sampleDuration = CATEGORY_CONFIG[CategoryEnum.PLAN_PROMOTION].options?.sampleDuration as number;

      Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
      prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).not.toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnCashbackPlus,
            order: 2,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });

      Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, 2 * sampleDuration).valueOf());
      prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).not.toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.PlanUpgradePlus,
            order: 2,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });

    it("should return different banners from learning guide promotion based on date", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({
        owner: user.id
      });

      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]])
      });

      const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
      Date.now = jest.fn(() => TODAY.valueOf());

      let prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.HowToThinkOfRisk,
            order: 2,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          }),
          expect.objectContaining({
            bannerId: BannerEnum.InvestingInThematics,
            order: 3,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });

      const sampleDuration = CATEGORY_CONFIG[CategoryEnum.LEARNING_GUIDE_PROMOTION].options
        ?.sampleDuration as number;

      Date.now = jest.fn(() => DateUtil.getDateAfterNdays(TODAY, sampleDuration).valueOf());
      prompts = await UserService.getPrompts(user, "banner");
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.NewToInvesting,
            order: 2,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          }),
          expect.objectContaining({
            bannerId: BannerEnum.GrowYourSavingsWithMoneyMarketFunds,
            order: 3,
            data: expect.objectContaining({
              title: expect.anything(),
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });

    it("should not return savings promotion banner if the user has savings holdings", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 100, currency: "GBX" }]])
      });
      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
      Date.now = jest.fn(() => TODAY.valueOf());

      const prompts = await UserService.getPrompts(user, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);
      expect(prompts).not.toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnInterestOnYourMoney,
            order: 1
          })
        ])
      });
    });

    it("should not return savings promotion banner if the user has no savings holdings but has a pending savings topup", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED
      });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 0, currency: "GBX" }]])
      });
      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });
      await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id
      });

      const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
      Date.now = jest.fn(() => TODAY.valueOf());

      const prompts = await UserService.getPrompts(user, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS);
      expect(prompts).not.toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnInterestOnYourMoney,
            order: 1
          })
        ])
      });
    });

    it("should return GBP savings promotion banner if the user has no savings holdings and zero pending savings topup & has UK company entity", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 0, currency: "GBX" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
      Date.now = jest.fn(() => TODAY.valueOf());

      const prompts = await UserService.getPrompts(user, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnInterestOnYourMoney,
            order: 1,
            data: expect.objectContaining({
              title: "Earn up to 4.60% interest on your money!",
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });

    it("should return EUR savings promotion banner if the user has no savings holdings and zero pending savings topup & has European company entity", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_eur", { amount: 0, currency: "EUC" }]]),
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      });
      await buildSubscription({
        owner: user.id,
        price: "free_monthly"
      });

      const TODAY = new Date("2023-09-09T11:00:00Z"); // Start of rotation
      Date.now = jest.fn(() => TODAY.valueOf());

      const prompts = await UserService.getPrompts(user, "banner");

      expect(prompts.bannerPrompts?.length).toEqual(MINIMUM_BANNER_PROMPTS_VERIFIED_USERS + 1);
      expect(prompts).toEqual({
        bannerPrompts: expect.arrayContaining([
          expect.objectContaining({
            bannerId: BannerEnum.EarnInterestOnYourMoney,
            order: 1,
            data: expect.objectContaining({
              title: "Earn up to 3.40% interest on your money!",
              modalTitle: expect.anything(),
              modalContent: expect.anything(),
              modalButtonText: expect.anything()
            })
          })
        ])
      });
    });
  });

  describe("markPromptsAsSeen", () => {
    let user: UserDocument;

    beforeAll(async () => {
      user = await buildUser();
    });

    afterAll(async () => await clearDb());

    it("should update reward document", async () => {
      const reward = await buildReward({
        hasViewedAppModal: false,
        targetUser: user.id
      });

      await UserService.markPromptsAsSeen({
        ids: [reward.id],
        modalType: "Reward"
      });

      const updatedReward = await Reward.findById(reward.id);

      expect(updatedReward?.hasViewedAppModal).toEqual(true);
    });

    it("should update multiple reward documents", async () => {
      const firstReward = await buildReward({
        hasViewedAppModal: false,
        targetUser: user.id
      });
      const secondReward = await buildReward({
        hasViewedAppModal: false,
        targetUser: user.id
      });

      await UserService.markPromptsAsSeen({
        ids: [firstReward.id, secondReward.id],
        modalType: "Reward"
      });

      const updatedFirstReward = await Reward.findById(firstReward.id);

      expect(updatedFirstReward?.hasViewedAppModal).toEqual(true);

      const updatedSecondReward = await Reward.findById(secondReward.id);

      expect(updatedSecondReward?.hasViewedAppModal).toEqual(true);
    });

    it("should update gift document", async () => {
      const gift = await buildGift({
        hasViewedAppModal: false,
        targetUserEmail: user.email
      });

      await UserService.markPromptsAsSeen({
        ids: [gift.id],
        modalType: "Gift"
      });

      const updatedGift = await Gift.findById(gift.id);

      expect(updatedGift?.hasViewedAppModal).toEqual(true);
    });

    it("should update multiple gift documents", async () => {
      const firstGift = await buildGift({
        hasViewedAppModal: false,
        targetUserEmail: user.email
      });
      const secondGift = await buildGift({
        hasViewedAppModal: false,
        targetUserEmail: user.email
      });

      await UserService.markPromptsAsSeen({
        ids: [firstGift.id, secondGift.id],
        modalType: "Gift"
      });

      const updatedFirstGift = await Gift.findById(firstGift.id);

      expect(updatedFirstGift?.hasViewedAppModal).toEqual(true);

      const updatedSecondGift = await Gift.findById(secondGift.id);

      expect(updatedSecondGift?.hasViewedAppModal).toEqual(true);
    });

    it("should update wealthyhood dividend transaction document", async () => {
      const wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
        hasViewedAppModal: false,
        owner: user.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        }
      });

      await UserService.markPromptsAsSeen({
        ids: [wealthyhoodDividend.id],
        modalType: "WealthyhoodDividend"
      });

      const updatedwealthyhoodDividend = await WealthyhoodDividendTransaction.findById(wealthyhoodDividend.id);

      expect(updatedwealthyhoodDividend?.hasViewedAppModal).toEqual(true);
    });

    it("should update multiple wealthyhood dividend transaction documents", async () => {
      const firstWealthyhoodDividend = await buildWealthyhoodDividendTransaction({
        hasViewedAppModal: false,
        owner: user.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        }
      });
      const secondWealthyhoodDividend = await buildWealthyhoodDividendTransaction({
        hasViewedAppModal: false,
        owner: user.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        }
      });

      await UserService.markPromptsAsSeen({
        ids: [firstWealthyhoodDividend.id, secondWealthyhoodDividend.id],
        modalType: "WealthyhoodDividend"
      });

      const updatedFirstwealthyhoodDividend = await WealthyhoodDividendTransaction.findById(
        firstWealthyhoodDividend.id
      );

      expect(updatedFirstwealthyhoodDividend?.hasViewedAppModal).toEqual(true);

      const updatedSecondwealthyhoodDividend = await WealthyhoodDividendTransaction.findById(
        firstWealthyhoodDividend.id
      );

      expect(updatedSecondwealthyhoodDividend?.hasViewedAppModal).toEqual(true);
    });
  });

  describe("getTransactionActivity", () => {
    let user: UserDocument;
    let bankAccount: BankAccountDocument;
    let portfolio: PortfolioDocument;
    let mandate: MandateDocument;
    let automation: AutomationDocument;

    const TODAY = new Date("2023-10-30T11:00:00Z");

    beforeAll(async () => {
      jest.clearAllMocks();
    });
    beforeEach(async () => {
      await clearDb();

      Date.now = jest.fn(() => TODAY.valueOf());

      user = await buildUser();
      await buildSubscription({ owner: user.id });
      bankAccount = await buildBankAccount({ owner: user.id });
      await user.populate("bankAccounts");
      portfolio = await buildPortfolio({ owner: user.id });
    });

    describe("when user has a pending Truelayer-backed deposit", () => {
      it("should return the pending deposit", async () => {
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            }
          },
          bankAccount: bankAccount.id
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Deposit",
          item: await DepositCashTransaction.findOne({
            _id: deposit._id
          })
            .populate("bankAccount")
            .populate("linkedAssetTransaction")
            .populate("linkedSavingsTopup")
        };
        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has a pending Saltedge-backed deposit", () => {
      it("should return the pending deposit", async () => {
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          status: "Pending",
          providers: {
            saltedge: {
              id: faker.string.uuid(),
              customId: faker.string.uuid(),
              status: "accepted"
            }
          },
          bankAccount: bankAccount.id
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Deposit",
          item: await DepositCashTransaction.findOne({
            _id: deposit._id
          })
            .populate("bankAccount")
            .populate("linkedAssetTransaction")
            .populate("linkedSavingsTopup")
        };
        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has a pending deposit linked to a portfolio buy", () => {
      it("should return the pending deposit", async () => {
        const deposit = await buildDepositCashTransaction(
          {
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toHaveLength(1);
      });
    });

    describe("when user has a cancelled deposit linked to portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "cancelled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id,
            owner: user.id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Cancelled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
        ).toHaveLength(1);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
        ).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a pending portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should return the deposit", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toEqual([
          expect.objectContaining({
            item: expect.objectContaining({ displayTag: DisplayTagEnum.INSTANT_INVEST })
          })
        ]);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
        ).toHaveLength(1);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
        ).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a cancelled portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Cancelled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should return the deposit", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toHaveLength(1);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Deposit"
          })
        );
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
        ).toHaveLength(1);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
        ).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a settled portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Settled"
        });
        deposit = await buildDepositCashTransaction({
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          settledAt: new Date(),
          bankAccount: user.bankAccounts[0].id,
          owner: user.id
        });

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should return the deposit", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toHaveLength(1);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
        ).toHaveLength(1);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
        ).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit", () => {
      it("should return the settled deposit", async () => {
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Deposit",
          item: await DepositCashTransaction.findOne({
            _id: deposit._id
          })
            .populate("bankAccount")
            .populate("linkedAssetTransaction")
            .populate("linkedSavingsTopup")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has a cancelled deposit", () => {
      it("should not return the cancelled deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "cancelled"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has a rejected deposit", () => {
      it("should not return the rejected deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "failed"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has a settled wealthyhood dividend", () => {
      it("should return the dividend", async () => {
        const wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Bonus",
          item: await WealthyhoodDividendTransaction.findOne({
            _id: wealthyhoodDividend._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has pending wealthyhood dividend", () => {
      it("should not return the dividend", async () => {
        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Created"
              }
            }
          }
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has rejected wealthyhood dividend", () => {
      it("should not return the dividend", async () => {
        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Rejected"
              }
            }
          }
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has NotStarted rebalance", () => {
      it("should return the rebalance transaction and should be cancellable", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "NotStarted"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id,
            isCancellable: true
          })
        );
      });
    });

    describe("when user has PendingBuy rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id,
            isCancellable: true
          })
        );
      });
    });

    describe("when user has PendingSell rebalance", () => {
      describe("and there are no orders", () => {
        it("should return the rebalance transaction and should be cancellable", async () => {
          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          const expectedTransaction = {
            type: "transaction",
            activityFilter: "Rebalance",
            item: await RebalanceTransaction.findOne({
              _id: rebalance._id
            }).populate("orders")
          };

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
            expect.objectContaining({
              id: expectedTransaction.item.id,
              isCancellable: true
            })
          );
        });
      });

      describe("and orders are NOT submitted", () => {
        it("should return the rebalance transaction and should be cancellable", async () => {
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
          ]);

          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          await Promise.all([
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_paypal"]?.isin
            }),
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_airbnb"]?.isin
            })
          ]);

          const expectedTransaction = {
            type: "transaction",
            activityFilter: "Rebalance",
            item: await RebalanceTransaction.findOne({
              _id: rebalance._id
            }).populate("orders")
          };
          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                type: expectedTransaction.type,
                activityFilter: expectedTransaction.activityFilter,
                item: expect.objectContaining({
                  id: expectedTransaction.item?.id,
                  isCancellable: true
                })
              })
            ])
          );
        });
      });

      describe("and orders are submitted", () => {
        it("should return the rebalance transaction and should not be cancellable", async () => {
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
          ]);

          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          await Promise.all([
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_paypal"]?.isin,
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
              }
            }),
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_airbnb"]?.isin,
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
              }
            })
          ]);

          const expectedTransaction = {
            type: "transaction",
            activityFilter: "Rebalance",
            item: await RebalanceTransaction.findOne({
              _id: rebalance._id
            }).populate("orders")
          };
          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                type: expectedTransaction.type,
                activityFilter: expectedTransaction.activityFilter,
                item: expect.objectContaining({
                  id: expectedTransaction.item?.id,
                  isCancellable: false
                })
              })
            ])
          );
        });
      });
    });

    describe("when user has rejected rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Rejected"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id
          })
        );
      });
    });

    describe("when user has Cancelled rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Cancelled"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id
          })
        );
      });
    });

    describe("when user has Settled rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Settled"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id
          })
        );
      });
    });

    describe("when user has pending dividend", () => {
      it("should not return the dividend", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {}
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has cancelled dividend", () => {
      it("should not return the dividend", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Cancelled"
            }
          }
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has matched dividend", () => {
      it("should not return the dividend", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched"
            }
          }
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has settled dividend", () => {
      it("should return the dividend", async () => {
        const dividend = await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Dividends",
          item: await DividendTransaction.findOne({
            _id: dividend._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has not accepted reward", () => {
      it("should not return the reward", async () => {
        await buildReward({
          targetUser: user.id,
          accepted: false
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has accepted reward", () => {
      it("should return the reward", async () => {
        const reward = await buildReward({
          targetUser: user.id,
          accepted: true
        });

        const expectedReward = {
          type: "reward",
          activityFilter: "Rewards",
          item: await Reward.findOne({
            _id: reward._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedReward)))])
        );
      });
    });

    describe("when user has pending cashback", () => {
      it("should return the cashback", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });
        const cashback = await buildCashbackTransaction({
          owner: user.id,
          status: "Pending",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Bonus",
          item: await CashbackTransaction.findOne({
            _id: cashback._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has settled cashback", () => {
      it("should return the cashback", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });

        const cashback = await buildCashbackTransaction({
          owner: user.id,
          status: "Settled",
          activityFilter: "Bonus",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const expectedTransaction = {
          type: "transaction",
          item: await CashbackTransaction.findOne({
            _id: cashback._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has cancelled cashback", () => {
      it("should not return the cashback", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          status: "Cancelled",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has active withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has pending withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has cancelling withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Cancelling" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has withdrawal with no wealthkernel status", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: {}
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has cancelled withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Cancelled" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has rejected withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Rejected" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
        expect(receivedTransactions[0].item.status).toEqual("Pending");
      });
    });

    describe("when user has settled withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has any type of charge", () => {
      it("should not return any transaction", async () => {
        const subscription = await buildSubscription({
          owner: user.id,
          price: "paid_low_monthly",
          category: "FeeBasedSubscription"
        });

        await Promise.all([
          buildChargeTransaction({
            owner: user.id
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "commission"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "custody"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "fx"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "executionSpread"
          }),
          buildChargeTransaction({
            chargeMethod: "direct-debit",
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending",
            consideration: {
              currency: "GBP",
              amount: Decimal.mul(1, 100).toNumber()
            },
            subscription: subscription.id
          })
        ]);

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has a pending sell transaction", () => {
      it("should return the transaction", async () => {
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending",
          consideration: {
            currency: "USD"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();
        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount:
              (transaction?.orders[0] as OrderDocument)?.quantity *
              investmentProduct.currentTicker.pricePerCurrency["GBP"] *
              100,
            displayQuantity: 1
          })
        );

        expect(receivedTransaction).not.toEqual(
          expect.objectContaining({
            executionProgress: expect.anything()
          })
        );

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a pending sell transaction and one order is matched", () => {
      it("should return the transaction", async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_us", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_uk", listed: true })
        ]);

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending",
          consideration: {
            currency: "USD"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          }),
          await buildOrder({
            status: "Matched",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_uk"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();
        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            executionProgress: expect.objectContaining({
              label: "Partially executed",
              total: 2,
              matched: 1
            })
          })
        );

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a settled sell transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item;

        expect(receivedTransaction.id).toEqual(transaction.id);
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            activityFilter: "Sell",
            displayAmount: transaction.consideration?.amount,
            displayQuantity: undefined
          })
        );
      });
    });

    describe("when user has a cancelled sell transaction", () => {
      it("should return transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Cancelled",
          originalInvestmentAmount: 150
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.originalInvestmentAmount,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a rejected sell transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us" });

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Rejected",
          originalInvestmentAmount: 100
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Rejected", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.originalInvestmentAmount,
            displayQuantity: undefined
          })
        );
        expect((receivedTransactions[0].item as AssetTransactionDocument)?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: undefined,
            displayQuantity: transaction.orders[0].quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Sell"
        });
      });
    });

    describe("when user has a settled buy transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          originalInvestmentAmount: 100,
          consideration: {
            amount: 100, // stored in cents
            currency: "GBP"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.consideration.amount,
            displayQuantity: undefined
          })
        );
        expect((receivedTransactions[0].item as AssetTransactionDocument)?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Buy"
        });
      });
    });

    describe("when user has a pending single asset sell transaction, but the order is matched", () => {
      it("should return the transaction without execution progress", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;

        expect(receivedTransaction).not.toEqual(
          expect.objectContaining({
            executionProgress: expect.anything()
          })
        );
      });
    });

    describe("when user has a settled single asset sell transaction", () => {
      it("should return the transaction with activityFilter Sell", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: receivedTransaction.orders[0]?.displayAmount,
            displayQuantity: receivedTransaction.orders[0]?.displayQuantity
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Sell"
        });
        expect(receivedTransactions[0].item.id).toEqual(transaction.id);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a settled single asset buy transaction", () => {
      it("should return the transaction with activityFilter Buy", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: receivedTransaction.orders[0]?.displayAmount,
            displayQuantity: receivedTransaction.orders[0]?.displayQuantity
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Buy"
        });
      });
    });

    describe("when user has a cancelled buy transaction", () => {
      it("should return the transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          status: "Cancelled"
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 1000,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a rejected buy transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Rejected",
          originalInvestmentAmount: 110,
          consideration: {
            amount: 100, // stored in cents
            currency: "GBP"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Rejected", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 110,
            displayQuantity: undefined
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a pending gift buy transaction", () => {
      it("should return the transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "PendingGift",
          originalInvestmentAmount: 1250
        });

        await buildInvestmentProduct(true, { assetId: "equities_us" });

        const order = await buildOrder({
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 1240,
            originalAmount: 1250,
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 1250,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a pending deposit buy transaction", () => {
      describe("and the deposit has authorized status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorized"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
          ).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(1);
        });
      });

      describe("and the deposit has executed status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "executed"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
          ).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(1);
        });
      });

      describe("and the deposit has authorization_required status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorization_required"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has authorizing status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorizing"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });
    });

    describe("when user has a repeating investment that is pending deposit", () => {
      describe("and the deposit has Pending status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Pending"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collecting status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collecting"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collecting status in wealthkernel but it is the first for its mandate", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collecting"
                  }
                }
              },
              settledAt: new Date(),
              createdWhilePendingMandate: true,
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collected status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          const TODAY = new Date("2024-12-18");
          Date.now = jest.fn(() => +TODAY);

          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(TODAY, 5),
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                collectionRequestDate: DateUtil.getDateOfDaysAgo(TODAY, 3),
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collected"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
          ).toEqual(
            expect.objectContaining({
              activityFilter: "Buy",
              item: expect.objectContaining({
                pendingDeposit: expect.objectContaining({
                  isDirectDebitPaymentCollected: true,
                  directDebitProgressPercentage: 0.75
                })
              })
            })
          );
        });

        it("should return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toEqual([
            expect.objectContaining({
              item: expect.objectContaining({ displayTag: DisplayTagEnum.AUTOPILOT })
            })
          ]);
        });
      });

      describe("and the deposit has no status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {}
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Completed status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Completed"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
          ).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(1);
        });
      });

      describe("and the deposit has Cancelled status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Cancelled"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "DepositFailed",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Failed status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Failed"
                  }
                }
              },
              settledAt: new Date(),
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "DepositFailed",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });
    });

    describe("when user has an asset transaction", () => {
      it("should return status 200 and estimated displayed amount & exchange rate for USD-traded asset", async () => {
        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92, // USD → EUR rate
            GBP: Decimal.div(1, FX_RATE).toNumber() // USD → GBP rate (1/1.27 ≈ 0.787)
          },
          EUR: {
            EUR: 1,
            GBP: 0.86, // EUR → GBP rate
            USD: 1.09 // EUR → USD rate
          },
          GBP: {
            GBP: 1,
            EUR: 1.16, // GBP → EUR rate
            USD: FX_RATE // GBP → USD rate
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_apple", listed: true });
        const pendingAssetSellTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });

        pendingAssetSellTransaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: pendingAssetSellTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            quantity: 1
          })
        ];
        await pendingAssetSellTransaction.save();

        const transactionsReceived = await UserService.getTransactionActivity(user);
        expect((transactionsReceived[0].item as AssetTransactionDocument).displayAmount).toEqual(
          pendingAssetSellTransaction.orders[0].quantity *
            investmentProduct.currentTicker.pricePerCurrency["GBP"] *
            100
        );

        const order = (transactionsReceived[0].item as AssetTransactionDocument).orders[0];

        // For sell orders with free plan, the spread is FX_TARGET_SPREADS.free and it's added multiplicatively
        // Expected rate = FX_RATE * (1 + FX_TARGET_SPREADS.free) rounded to 3 decimals
        const expectedRate = new Decimal(FX_RATE)
          .mul(1 + FX_TARGET_SPREADS.free)
          .toDecimalPlaces(3)
          .toNumber();

        expect(order).toEqual(
          expect.objectContaining({
            displayAmount: order.quantity * investmentProduct.currentTicker.pricePerCurrency["GBP"] * 100,
            displayExchangeRate: {
              rate: expectedRate,
              currency: "USD"
            }
          })
        );
      });

      it("should return status 200 and estimated displayed amount and NO exchange rate for GBP-traded asset", async () => {
        // Current FX rate GBP → USD is more realistic (~1.27)
        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92,
            GBP: Decimal.div(1, FX_RATE).toNumber()
          },
          EUR: {
            EUR: 1,
            GBP: 0.86,
            USD: 1.09
          },
          GBP: {
            GBP: 1,
            EUR: 1.16,
            USD: FX_RATE
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const pendingAssetSellTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });

        pendingAssetSellTransaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: pendingAssetSellTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: undefined,
            quantity: 1
          })
        ];
        await pendingAssetSellTransaction.save();

        const transactionsReceived = await UserService.getTransactionActivity(user);
        expect((transactionsReceived[0].item as AssetTransactionDocument).displayAmount).toEqual(
          pendingAssetSellTransaction.orders[0].quantity *
            investmentProduct.currentTicker.pricePerCurrency["GBP"] *
            100
        );

        const order = (transactionsReceived[0].item as AssetTransactionDocument).orders[0];
        expect(order).toEqual(
          expect.objectContaining({
            displayAmount: order.quantity * investmentProduct.currentTicker.pricePerCurrency["GBP"] * 100,
            displayExchangeRate: undefined
          })
        );
      });
    });

    describe("when user has past orders with active & deprecated ISIN", () => {
      const ACTIVE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock";
      const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock_deprecated_1";
      let transactionsReceived: TransactionActivityItemType[];

      beforeEach(async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: ACTIVE_ASSET_ID }),
          buildInvestmentProduct(false, { assetId: DEPRECATED_ASSET_ID, listed: false })
        ]);

        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92, // USD → EUR rate
            GBP: Decimal.div(1, FX_RATE).toNumber() // USD → GBP rate (1/1.27 ≈ 0.787)
          },
          EUR: {
            EUR: 1,
            GBP: 0.86, // EUR → GBP rate
            USD: 1.09 // EUR → USD rate
          },
          GBP: {
            GBP: 1,
            EUR: 1.16, // GBP → EUR rate
            USD: FX_RATE // GBP → USD rate
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransactionActiveIsin = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });
        assetTransactionActiveIsin.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetTransactionActiveIsin.id,
            isin: investmentUniverseConfig.ASSET_CONFIG[ACTIVE_ASSET_ID].isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];
        await assetTransactionActiveIsin.save();

        const assetTransactionDeprecatedIsin = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });
        assetTransactionDeprecatedIsin.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetTransactionActiveIsin.id,
            isin: investmentUniverseConfig.ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];
        await assetTransactionDeprecatedIsin.save();

        transactionsReceived = await UserService.getTransactionActivity(user);
      });

      it("should return the orders for both the active and the deprecated isin", async () => {
        expect(transactionsReceived.length).toBe(2);
        expect(
          transactionsReceived.map((order) => (order.item as AssetTransactionDocument).orders[0].isin)
        ).toEqual(
          expect.arrayContaining([ASSET_CONFIG[ACTIVE_ASSET_ID].isin, ASSET_CONFIG[DEPRECATED_ASSET_ID].isin])
        );
      });
    });

    describe("when user has a savings topup", () => {
      describe("and it has a pending deposit", () => {
        it("should not return the transaction", async () => {
          const [deposit, anotherDeposit] = await Promise.all([
            buildDepositCashTransaction({
              owner: user.id,
              portfolio: portfolio.id
            }),
            buildDepositCashTransaction({
              owner: user.id,
              portfolio: portfolio.id
            })
          ]);
          await Promise.all([
            await buildSavingsTopup({
              owner: user.id,
              portfolio: portfolio.id,
              pendingDeposit: deposit.id,
              status: "PendingDeposit"
            }),
            await buildSavingsTopup({
              owner: user.id,
              portfolio: portfolio.id,
              pendingDeposit: anotherDeposit.id,
              status: "Settled"
            })
          ]);

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it's linked to an incomplete deposit", () => {
        it("should not return the transaction", async () => {
          const deposit = await buildDepositCashTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                version: "v3",
                status: "authorization_required"
              }
            }
          });
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            pendingDeposit: deposit.id,
            status: "PendingDeposit"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it's linked to a savings dividend", () => {
        it("should not return the transaction", async () => {
          const savingsTopup = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled"
          });
          await buildSavingsDividend({
            owner: user.id,
            portfolio: portfolio.id,
            linkedSavingsTopup: savingsTopup.id,
            status: "Settled"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Pending' status", () => {
        it("should return the transaction", async () => {
          const savingsTopupWithPendingStatus = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithPendingStatus.id,
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithPendingStatus.consideration.amount,
                displayDate: savingsTopupWithPendingStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Pending' status with no orders", () => {
        it("should return the transaction", async () => {
          const savingsTopupWithPendingStatus = await buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              status: "Pending"
            },
            {},
            false
          );

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithPendingStatus.id,
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithPendingStatus.consideration.amount,
                displayDate: savingsTopupWithPendingStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Settled' status", () => {
        it("should return the transaction", async () => {
          const savingsTopupWithSettledStatus = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            settledAt: new Date()
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithSettledStatus.id,
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithSettledStatus.consideration.amount,
                displayDate: savingsTopupWithSettledStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Rejected' status", () => {
        it("should return the transaction and ovewrite the status to 'Pending'", async () => {
          const savingsTopupWithRejectedStatus = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithRejectedStatus.id,
                status: "Pending",
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithRejectedStatus.consideration.amount,
                displayDate: savingsTopupWithRejectedStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Cancelled' status", () => {
        it("should return the transaction and ovewrite the status to 'Pending'", async () => {
          const savingsTopupWithCancelledStatus = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Cancelled"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithCancelledStatus.id,
                status: "Pending",
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithCancelledStatus.consideration.amount,
                displayDate: savingsTopupWithCancelledStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'DepositFailed' status", () => {
        it("should not return the transaction", async () => {
          const deposit = await buildDepositCashTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            pendingDeposit: deposit.id,
            status: "DepositFailed"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });
    });

    describe("when user has a savings withdrawal", () => {
      describe("and it has 'Pending' status", () => {
        it("should return the transaction", async () => {
          const savingsWithdrawalWithPendingStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithPendingStatus.id,
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithPendingStatus.consideration.amount,
                displayDate: savingsWithdrawalWithPendingStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Pending' status with no orders", () => {
        it("should return the transaction", async () => {
          const savingsWithdrawalWithPendingStatus = await buildSavingsWithdrawal(
            {
              owner: user.id,
              portfolio: portfolio.id,
              status: "Pending"
            },
            {},
            false
          );

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithPendingStatus.id,
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithPendingStatus.consideration.amount,
                displayDate: savingsWithdrawalWithPendingStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'PendingTopUp' status", () => {
        it("should return the transaction", async () => {
          const pendignSavingTopup = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            createdAt: new Date("2024-01-01")
          });
          const savingsWithdrawalWithPendingTopupStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "PendingTopUp",
            createdAt: new Date("2024-01-02")
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithPendingTopupStatus.id,
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithPendingTopupStatus.consideration.amount,
                displayDate: savingsWithdrawalWithPendingTopupStatus.createdAt
              })
            },
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: pendignSavingTopup.id,
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: pendignSavingTopup.consideration.amount,
                displayDate: pendignSavingTopup.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Settled' status", () => {
        it("should return the transaction", async () => {
          const savingsWithdrawalWithSettledStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            settledAt: new Date()
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithSettledStatus.id,
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithSettledStatus.consideration.amount,
                displayDate: savingsWithdrawalWithSettledStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Rejected' status", () => {
        it("should return the transaction and ovewrite the status to 'Pending'", async () => {
          const savingsWithdrawalWithRejectedStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);
          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithRejectedStatus.id,
                status: "Pending",
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithRejectedStatus.consideration.amount,
                displayDate: savingsWithdrawalWithRejectedStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Cancelled' status", () => {
        it("should return the transaction and ovewrite the status to 'Pending'", async () => {
          const savingsWithdrawalWithCancelledStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Cancelled"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);
          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithCancelledStatus.id,
                status: "Pending",
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithCancelledStatus.consideration.amount,
                displayDate: savingsWithdrawalWithCancelledStatus.createdAt
              })
            }
          ]);
        });
      });
    });

    describe("when user has a savings dividend", () => {
      it("should not return the transaction", async () => {
        await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions).toHaveLength(0);
      });
    });
  });

  describe("getInvestmentActivity", () => {
    let user: UserDocument;
    let bankAccount: BankAccountDocument;
    let portfolio: PortfolioDocument;
    let mandate: MandateDocument;
    let automation: AutomationDocument;

    const TODAY = new Date("2023-10-30T11:00:00Z");

    beforeAll(async () => {
      jest.clearAllMocks();
    });
    beforeEach(async () => {
      await clearDb();

      Date.now = jest.fn(() => TODAY.valueOf());

      user = await buildUser();
      await buildSubscription({ owner: user.id });
      bankAccount = await buildBankAccount({ owner: user.id });
      await user.populate("bankAccounts");
      portfolio = await buildPortfolio({ owner: user.id });
    });

    // buy

    describe("when user has a pending deposit linked to a portfolio buy", () => {
      describe("and the deposit has authorized status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorized"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has executed status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "executed"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has authorization_required status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorization_required",
                  version: "v3"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has authorizing status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorizing",
                  version: "v3"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });
    });

    describe("when user has a cancelled deposit linked to portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "cancelled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id,
            owner: user.id,
            depositAction: DepositActionEnum.DEPOSIT_AND_INVEST
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Cancelled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)).toHaveLength(
          1
        );
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a pending portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions.filter((transaction) => transaction.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)).toHaveLength(
          1
        );
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a cancelled portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Cancelled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)).toHaveLength(
          1
        );
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
          expect.objectContaining({
            investmentActivityFilter: TransactionInvestmentActivityFilterEnum.Buy
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a settled portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Settled"
        });
        deposit = await buildDepositCashTransaction({
          depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          settledAt: new Date(),
          bankAccount: user.bankAccounts[0].id,
          owner: user.id
        });

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)).toHaveLength(
          1
        );
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled buy transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          originalInvestmentAmount: 100,
          consideration: {
            amount: 100, // stored in cents
            currency: "GBP"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.consideration.amount,
            displayQuantity: undefined
          })
        );
        expect((receivedTransactions[0] as AssetTransactionDocument)?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Buy"
        });
      });
    });

    describe("when user has a settled single asset buy transaction", () => {
      it("should return the transaction with activityFilter Buy", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: receivedTransaction.orders[0]?.displayAmount,
            displayQuantity: receivedTransaction.orders[0]?.displayQuantity
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Buy"
        });
      });
    });

    describe("when user has a cancelled buy transaction", () => {
      it("should return the transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          status: "Cancelled"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 1000,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a rejected buy transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Rejected",
          originalInvestmentAmount: 110,
          consideration: {
            amount: 100, // stored in cents
            currency: "GBP"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Rejected", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 110,
            displayQuantity: undefined
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a pending gift buy transaction", () => {
      it("should return the transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "PendingGift",
          originalInvestmentAmount: 1250
        });

        await buildInvestmentProduct(true, { assetId: "equities_us" });

        const order = await buildOrder({
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 1240,
            originalAmount: 1250,
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 1250,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a repeating investment that is pending deposit", () => {
      describe("and the deposit has Pending status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Pending"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collecting status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collecting"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collecting status in wealthkernel but it is the first for its mandate", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collecting"
                  }
                }
              },
              settledAt: new Date(),
              createdWhilePendingMandate: true,
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collected status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          const TODAY = new Date("2024-12-18");
          Date.now = jest.fn(() => +TODAY);

          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(TODAY, 5),
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                collectionRequestDate: DateUtil.getDateOfDaysAgo(TODAY, 3),
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collected"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              pendingDeposit: expect.objectContaining({
                isDirectDebitPaymentCollected: true,
                directDebitProgressPercentage: 0.75
              })
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has no status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {}
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Completed status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Completed"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Cancelled status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Cancelled"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "DepositFailed",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Failed status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Failed"
                  }
                }
              },
              settledAt: new Date(),
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "DepositFailed",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });
    });

    describe("when user has an asset transaction and we are considering the exchange rates", () => {
      it("should return status 200 and estimated displayed amount & exchange rate for USD-traded asset", async () => {
        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92, // USD → EUR rate
            GBP: Decimal.div(1, FX_RATE).toNumber() // USD → GBP rate (1/1.27 ≈ 0.787)
          },
          EUR: {
            EUR: 1,
            GBP: 0.86, // EUR → GBP rate
            USD: 1.09 // EUR → USD rate
          },
          GBP: {
            GBP: 1,
            EUR: 1.16, // GBP → EUR rate
            USD: FX_RATE // GBP → USD rate
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_apple", listed: true });
        const pendingAssetSellTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });

        pendingAssetSellTransaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: pendingAssetSellTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            quantity: 1
          })
        ];
        await pendingAssetSellTransaction.save();

        const transactionsReceived = await UserService.getInvestmentActivity(user);
        expect((transactionsReceived[0] as AssetTransactionDocument).displayAmount).toEqual(
          pendingAssetSellTransaction.orders[0].quantity *
            investmentProduct.currentTicker.pricePerCurrency["GBP"] *
            100
        );

        const order = (transactionsReceived[0] as AssetTransactionDocument).orders[0];

        // For sell orders with free plan, the spread is FX_TARGET_SPREADS.free and it's added multiplicatively
        // Expected rate = FX_RATE * (1 + FX_TARGET_SPREADS.free) rounded to 3 decimals
        const expectedRate = new Decimal(FX_RATE)
          .mul(1 + FX_TARGET_SPREADS.free)
          .toDecimalPlaces(3)
          .toNumber();

        expect(order).toEqual(
          expect.objectContaining({
            displayAmount: order.quantity * investmentProduct.currentTicker.pricePerCurrency["GBP"] * 100,
            displayExchangeRate: {
              rate: expectedRate,
              currency: "USD"
            }
          })
        );
      });

      it("should return status 200 and estimated displayed amount and NO exchange rate for GBP-traded asset", async () => {
        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92,
            GBP: Decimal.div(1, FX_RATE).toNumber()
          },
          EUR: {
            EUR: 1,
            GBP: 0.86,
            USD: 1.09
          },
          GBP: {
            GBP: 1,
            EUR: 1.16,
            USD: FX_RATE
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const pendingAssetSellTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });

        pendingAssetSellTransaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: pendingAssetSellTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: undefined,
            quantity: 1
          })
        ];
        await pendingAssetSellTransaction.save();

        const transactionsReceived = await UserService.getInvestmentActivity(user);
        expect((transactionsReceived[0] as AssetTransactionDocument).displayAmount).toEqual(
          pendingAssetSellTransaction.orders[0].quantity *
            investmentProduct.currentTicker.pricePerCurrency["GBP"] *
            100
        );

        const order = (transactionsReceived[0] as AssetTransactionDocument).orders[0];
        expect(order).toEqual(
          expect.objectContaining({
            displayAmount: order.quantity * investmentProduct.currentTicker.pricePerCurrency["GBP"] * 100,
            displayExchangeRate: undefined
          })
        );
      });
    });

    describe("when user has past orders with active & deprecated ISIN", () => {
      const ACTIVE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock";
      const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock_deprecated_1";
      let transactionsReceived: (TransactionDocument | RewardDocument)[];

      beforeEach(async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: ACTIVE_ASSET_ID }),
          buildInvestmentProduct(false, { assetId: DEPRECATED_ASSET_ID, listed: false })
        ]);

        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92, // USD → EUR rate
            GBP: Decimal.div(1, FX_RATE).toNumber() // USD → GBP rate (1/1.27 ≈ 0.787)
          },
          EUR: {
            EUR: 1,
            GBP: 0.86, // EUR → GBP rate
            USD: 1.09 // EUR → USD rate
          },
          GBP: {
            GBP: 1,
            EUR: 1.16, // GBP → EUR rate
            USD: FX_RATE // GBP → USD rate
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransactionActiveIsin = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });
        assetTransactionActiveIsin.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetTransactionActiveIsin.id,
            isin: investmentUniverseConfig.ASSET_CONFIG[ACTIVE_ASSET_ID].isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];
        await assetTransactionActiveIsin.save();

        const assetTransactionDeprecatedIsin = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });
        assetTransactionDeprecatedIsin.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetTransactionActiveIsin.id,
            isin: investmentUniverseConfig.ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];
        await assetTransactionDeprecatedIsin.save();

        transactionsReceived = await UserService.getInvestmentActivity(user);
      });

      it("should return the orders for both the active and the deprecated isin", async () => {
        expect(transactionsReceived.length).toBe(2);
        expect(transactionsReceived.map((order) => (order as AssetTransactionDocument).orders[0].isin)).toEqual(
          expect.arrayContaining([ASSET_CONFIG[ACTIVE_ASSET_ID].isin, ASSET_CONFIG[DEPRECATED_ASSET_ID].isin])
        );
      });
    });

    // sell
    describe("when user has a pending sell transaction", () => {
      it("should return the transaction", async () => {
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending",
          consideration: {
            currency: "USD"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount:
              (transaction?.orders[0] as OrderDocument)?.quantity *
              investmentProduct.currentTicker.pricePerCurrency["GBP"] *
              100,
            displayQuantity: 1
          })
        );

        expect(receivedTransaction).not.toEqual(
          expect.objectContaining({
            executionProgress: expect.anything()
          })
        );

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a pending sell transaction and one order is matched", () => {
      it("should return the transaction", async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_us", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_uk", listed: true })
        ]);

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Pending",
          consideration: {
            currency: "USD"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          }),
          await buildOrder({
            status: "Matched",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_uk"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            executionProgress: expect.objectContaining({
              label: "Partially executed",
              total: 2,
              matched: 1
            })
          })
        );

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            investmentActivityFilter: TransactionInvestmentActivityFilterEnum.Sell
          })
        );
      });
    });

    describe("when user has a settled sell transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0];

        expect(receivedTransaction.id).toEqual(transaction.id);
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            activityFilter: "Sell",
            displayAmount: transaction.consideration?.amount,
            displayQuantity: undefined
          })
        );
      });
    });

    describe("when user has a cancelled sell transaction", () => {
      it("should return transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Cancelled",
          originalInvestmentAmount: 150
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.originalInvestmentAmount,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a rejected sell transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us" });

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Rejected",
          originalInvestmentAmount: 100
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Rejected", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.originalInvestmentAmount,
            displayQuantity: undefined
          })
        );
        expect((receivedTransactions[0] as AssetTransactionDocument)?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: undefined,
            displayQuantity: transaction.orders[0].quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Sell"
        });
      });
    });

    describe("when user has a pending single asset sell transaction, but the order is matched", () => {
      it("should return the transaction without execution progress", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;

        expect(receivedTransaction).not.toEqual(
          expect.objectContaining({
            executionProgress: expect.anything()
          })
        );
      });
    });

    describe("when user has a settled single asset sell transaction", () => {
      it("should return the transaction with activityFilter Sell", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: receivedTransaction.orders[0]?.displayAmount,
            displayQuantity: receivedTransaction.orders[0]?.displayQuantity
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Sell"
        });
        expect(receivedTransactions[0].id).toEqual(transaction.id);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    // rebalance
    describe("when user has NotStarted rebalance", () => {
      it("should return the rebalance transaction and should be cancellable", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "NotStarted"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: rebalance.id,
            isCancellable: true
          })
        );
      });
    });

    describe("when user has PendingBuy rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: rebalance.id,
            isCancellable: true
          })
        );
      });
    });

    describe("when user has PendingSell rebalance", () => {
      describe("and there are no orders", () => {
        it("should return the rebalance transaction and should be cancellable", async () => {
          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
            expect.objectContaining({
              id: rebalance.id,
              isCancellable: true
            })
          );
        });
      });

      describe("and orders are NOT submitted", () => {
        it("should return the rebalance transaction and should be cancellable", async () => {
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
          ]);

          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          await Promise.all([
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_paypal"]?.isin
            }),
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_airbnb"]?.isin
            })
          ]);

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                id: rebalance.id,
                isCancellable: true
              })
            ])
          );
        });
      });

      describe("and orders are submitted", () => {
        it("should return the rebalance transaction and should not be cancellable", async () => {
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
          ]);

          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          await Promise.all([
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_paypal"]?.isin,
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
              }
            }),
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_airbnb"]?.isin,
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
              }
            })
          ]);

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                id: rebalance.id,
                isCancellable: false
              })
            ])
          );
        });
      });
    });

    describe("when user has rejected rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Rejected"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: rebalance.id
          })
        );
      });
    });

    describe("when user has Cancelled rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Cancelled"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: rebalance.id
          })
        );
      });
    });

    describe("when user has Settled rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Settled"
        });

        const expectedTransaction = (await RebalanceTransaction.findOne({
          _id: rebalance._id
        }).populate("orders")) as RebalanceTransactionDocument;

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.id
          })
        );
      });
    });

    // stock dividends

    describe("when the user has stock dividends", () => {
      it("should not return pending dividends", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {}
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should not return cancelled dividends", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Cancelled"
            }
          }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should return settled dividends", async () => {
        const dividend = await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(dividend)))])
        );
      });
    });

    // rewards

    describe("when the user has rewards", () => {
      it("should not return not accepted rewards", async () => {
        await buildReward({
          targetUser: user.id,
          accepted: false
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should return accepted rewards", async () => {
        const reward = await buildReward({
          targetUser: user.id,
          accepted: true
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(reward)))])
        );
      });
    });

    // skipped - cashbacks
    describe("when user has cashback transactions", () => {
      it("should not return pending cashbacks", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });
        await buildCashbackTransaction({
          owner: user.id,
          status: "Pending",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return settled cashbacks", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          status: "Settled",
          activityFilter: "Bonus",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return cancelled cashbacks", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          status: "Cancelled",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });
    });

    // skipped - wealthyhood plan dividend
    describe("when the user has wealthyhood plan dividends", () => {
      it("should not return settled dividends", async () => {
        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return pending dividends", async () => {
        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Created"
              }
            }
          }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    // skipped transactions - deposits
    describe("when user has deposit transactions", () => {
      it("should not return the settled deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should not return the cancelled deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "cancelled"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should not return the rejected deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "failed"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    // skipped transactions - withdrawals
    describe("when user has withdrawal transactions", () => {
      it("should not return pending withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return cancelling withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Cancelling" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return cancelled withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Cancelled" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return rejected withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Rejected" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return settled withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });
    });

    // skipped transactions - charges
    describe("when user has any type of charge", () => {
      it("should not return any transaction", async () => {
        const subscription = await buildSubscription({
          owner: user.id,
          price: "paid_low_monthly",
          category: "FeeBasedSubscription"
        });

        await Promise.all([
          buildChargeTransaction({
            owner: user.id
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "commission"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "custody"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "fx"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "executionSpread"
          }),
          buildChargeTransaction({
            chargeMethod: "direct-debit",
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending",
            consideration: {
              currency: "GBP",
              amount: Decimal.mul(1, 100).toNumber()
            },
            subscription: subscription.id
          })
        ]);

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    // skipped transactions - savings

    describe("when user has a savings topup", () => {
      describe("and it has a pending deposit", () => {
        it("should not return the transaction", async () => {
          const [deposit, anotherDeposit] = await Promise.all([
            buildDepositCashTransaction({
              owner: user.id,
              portfolio: portfolio.id
            }),
            buildDepositCashTransaction({
              owner: user.id,
              portfolio: portfolio.id
            })
          ]);
          await Promise.all([
            await buildSavingsTopup({
              owner: user.id,
              portfolio: portfolio.id,
              pendingDeposit: deposit.id,
              status: "PendingDeposit"
            }),
            await buildSavingsTopup({
              owner: user.id,
              portfolio: portfolio.id,
              pendingDeposit: anotherDeposit.id,
              status: "Settled"
            })
          ]);

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Pending' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Pending' status with no orders", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              status: "Pending"
            },
            {},
            false
          );

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Settled' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            settledAt: new Date()
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Rejected' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Cancelled' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Cancelled"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'DepositFailed' status", () => {
        it("should not return the transaction", async () => {
          const deposit = await buildDepositCashTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            pendingDeposit: deposit.id,
            status: "DepositFailed"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });
    });

    describe("when user has a savings withdrawal", () => {
      describe("and it has 'Pending' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Pending' status with no orders", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal(
            {
              owner: user.id,
              portfolio: portfolio.id,
              status: "Pending"
            },
            {},
            false
          );

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'PendingTopUp' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            createdAt: new Date("2024-01-01")
          });
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "PendingTopUp",
            createdAt: new Date("2024-01-02")
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Settled' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            settledAt: new Date()
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Rejected' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Cancelled' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Cancelled"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });
    });

    describe("when user has a savings dividend", () => {
      it("should not return the transaction", async () => {
        await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions).toHaveLength(0);
      });
    });
  });

  describe("setPassedKycStatusIfEligible", () => {
    describe("when user has active WK account and passed KYC operation but is flagged for passport", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ isPassportVerified: false });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should leave the status to 'pending'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user has active WK account and passed KYC operation but is potentially duplicate", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ isPotentiallyDuplicateAccount: true });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should leave the status to 'pending'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user is eligible but passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          isPassportVerified: undefined,
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
          buildApplicant({
            info: {
              firstName: user.firstName,
              lastName: user.lastName,
              dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
              nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]],
              idDocs: [{ idDocType: "PASSPORT", mrzLine1: "MRZMRZMRZ" }]
            }
          })
        );
      });
      afterEach(async () => await clearDb());

      it("should make the subscription 'active'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const subscription = await Subscription.findOne({ owner: user.id });

        expect(subscription?.active).toEqual(true);
      });

      it("should set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user hasn't submitted all required info and passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          isPassportVerified: undefined,
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        }); // User missing address details
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(buildApplicant());
      });
      afterEach(async () => await clearDb());

      it("should not set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user is Greek and in EU entity and has used old ID card and passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          isPassportVerified: undefined,
          nationalities: ["GR"],
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
          buildApplicant({
            info: {
              firstName: user.firstName,
              lastName: user.lastName,
              dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
              nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]],
              idDocs: [{ idDocType: "ID_CARD" }]
            }
          })
        );
      });
      afterEach(async () => await clearDb());

      it("should set the status to 'failed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.FAILED);
      });
    });

    describe("when user is Greek and in UK entity and has used old ID card and passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          isPassportVerified: undefined,
          nationalities: ["GR"],
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
          buildApplicant({
            info: {
              firstName: user.firstName,
              lastName: user.lastName,
              dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
              nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]],
              idDocs: [{ idDocType: "ID_CARD" }]
            }
          })
        );
      });
      afterEach(async () => await clearDb());

      it("should set the status to 'failed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user is Greek and in EU entity and has used new ID card and passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          isPassportVerified: undefined,
          nationalities: ["GR"],
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
          buildApplicant({
            info: {
              firstName: user.firstName,
              lastName: user.lastName,
              dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
              nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]],
              idDocs: [{ idDocType: "ID_CARD", mrzLine1: "MRZMRZMRZ" }]
            }
          })
        );
      });
      afterEach(async () => await clearDb());

      it("should set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user has active WK account and passed KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should make the subscription 'active'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const subscription = await Subscription.findOne({ owner: user.id });

        expect(subscription?.active).toEqual(true);
      });

      it("should set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user has active WK account and manually passed KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "ManuallyPassed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "RED"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should make the subscription 'active'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const subscription = await Subscription.findOne({ owner: user.id });

        expect(subscription?.active).toEqual(true);
      });

      it("should set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user has a non active WK account and passed KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
          }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should not set the status to 'passed'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user has an active WK account and a non-passed KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Failed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "RED"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should not set the status to 'passed'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user does not have a KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
      });
      afterEach(async () => await clearDb());

      it("should not set the status to 'passed'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user was KYC failed but now his WK account is active and passed a KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          kycStatus: KycStatusEnum.FAILED,
          kycFailedAt: new Date()
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should make the subscription 'active'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const subscription = await Subscription.findOne({ owner: user.id });

        expect(subscription?.active).toEqual(true);
      });

      it("should create a risk assessment for that user", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const riskAssessments: RiskAssessmentDocument[] = await RiskAssessment.find();
        expect(riskAssessments.length).toBe(1);
      });

      it("should set the status to 'passed' and remove kycFailedAt", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
        expect(updatedUser?.kycFailedAt).toBeUndefined();
      });
    });
  });

  describe("updatePassportDetailsOnlyIfMissing", () => {
    describe("when the user has already submitted passport details", () => {
      let user: UserDocument;

      const APPLICANT_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();

        await UserService.updateKycProviderDataIfMissing(user.id, APPLICANT_ID, {
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" }),
          nationality: "GB"
        });
      });
      afterAll(async () => await clearDb());

      it("should not overwrite the existing passport details", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.firstName).toEqual(user.firstName);
        expect(updatedUser?.lastName).toEqual(user.lastName);
        expect(updatedUser?.dateOfBirth).toEqual(user.dateOfBirth);
        expect(updatedUser?.nationalities).toEqual(user.nationalities);
      });

      it("should not emit any event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when the user hasn't submitted passport details previously", () => {
      let user: UserDocument;

      const APPLICANT_ID = faker.string.uuid();
      const PASSPORT_DETAILS = {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" }),
        nationality: "GB" as countriesConfig.CountryCodesType
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser({}, false, true);

        await UserService.updateKycProviderDataIfMissing(user.id, APPLICANT_ID, PASSPORT_DETAILS);
      });
      afterAll(async () => await clearDb());

      it("should update user with new passport details", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.providers?.sumsub?.id).toEqual(APPLICANT_ID);
        expect(updatedUser?.firstName).toEqual(PASSPORT_DETAILS.firstName);
        expect(updatedUser?.lastName).toEqual(PASSPORT_DETAILS.lastName);
        expect(updatedUser?.dateOfBirth).toEqual(PASSPORT_DETAILS.dateOfBirth);
        expect(updatedUser?.nationalities).toEqual([PASSPORT_DETAILS.nationality]);
      });

      it("should emit passport details event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.passportDetailsSubmission.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when the user hasn't submitted passport details previously but nationality is missing", () => {
      let user: UserDocument;
      const PASSPORT_DETAILS = {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" })
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser({}, false, true);

        await UserService.updateKycProviderDataIfMissing(user.id, faker.string.uuid(), PASSPORT_DETAILS);
      });
      afterAll(async () => await clearDb());

      it("should update user with new passport details", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.firstName).toEqual(PASSPORT_DETAILS.firstName);
        expect(updatedUser?.lastName).toEqual(PASSPORT_DETAILS.lastName);
        expect(updatedUser?.dateOfBirth).toEqual(PASSPORT_DETAILS.dateOfBirth);
        expect(updatedUser?.nationalities).toEqual([]);
      });

      it("should not emit any event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });
  });

  describe("setFailedKycStatus", () => {
    describe("when the user has submitted required info and accepted terms", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser({ hasAcceptedTerms: true });
        await buildAddress({ owner: user.id });
        await buildSubscription({ owner: user.id, active: true });
      });
      afterAll(async () => await clearDb());

      it("should set the status to 'failed'", async () => {
        const updatedUser = await UserService.setFailedKycStatus(user);

        expect(updatedUser.kycStatus).toEqual(KycStatusEnum.FAILED);
      });

      it("should deactivate user's subscription", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: user.id });

        expect(updatedSubscription?.active).toEqual(false);
      });

      it("should emit a verification failure event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.verificationFailure.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should emit a 'whAccountStatusUpdate' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({
            id: user.id
          }),
          expect.objectContaining({
            accountStatus: MixpanelAccountStatusEnum.VerificationFailed
          })
        );
      });
    });
  });

  describe("getAccountStatementActivity", () => {
    describe("when user has no activity", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return empty array", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has an asset transaction with both cancelled / matched orders", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_us", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_uk", listed: true })
        ]);

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });

        assetTransaction.orders = await Promise.all([
          buildOrder({
            side: "Buy",
            status: "Matched",
            transaction: assetTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin
          }),
          buildOrder({
            side: "Buy",
            status: "Cancelled",
            transaction: assetTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin
          })
        ]);
        await assetTransaction.save();
      });
      afterAll(async () => await clearDb());

      it("should return only matched order", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            asset: "iShares FTSE 100 UCITS ETF DIST £ (ISF)"
          })
        ]);
      });
    });

    describe("when user has a settled deposit transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          consideration: {
            amount: 1000,
            currency: "GBP"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the deposit", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "deposit"
          })
        ]);
      });
    });

    describe("when user has a pending deposit transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 1000,
            currency: "GBP"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the deposit", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled withdrawal transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWithdrawalCashTransaction(
          {
            owner: user.id,
            portfolio: portfolio.id,
            settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            consideration: {
              amount: 1000,
              currency: "GBP"
            }
          },
          false
        );
      });
      afterAll(async () => await clearDb());

      it("should return the withdrawal", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "withdrawal"
          })
        ]);
      });
    });

    describe("when user has a pending cashback transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          linkedAssetTransaction: assetTransaction.id
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the cashback", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled cashback transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          linkedAssetTransaction: assetTransaction.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return the cashback", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "cashback"
          })
        ]);
      });
    });

    describe("when user has a pending WH dividend transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the WH dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled WH dividend transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the WH dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "bonus"
          })
        ]);
      });
    });

    describe("when user has a pending dividend transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          asset: "equities_uk"
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled dividend transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          asset: "equities_uk",
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "dividend"
          })
        ]);
      });
    });

    describe("when user has a pending withdrawal transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWithdrawalCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          consideration: {
            amount: 1000,
            currency: "GBP"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the withdrawal", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when the user has a settled reinvested savings dividend", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        const topup = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
          consideration: { amount: 100, currency: "GBP" }
        });
        await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          consideration: { amount: 100, currency: "GBP" },
          originalDividendAmount: 110,
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
          linkedSavingsTopup: topup.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return both savings transactions (top-up and dividend)", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);

        expect(activity).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              amount: 1,
              asset: "ICS Sterling Liquidity Fund (Premier)",
              currency: "GBP",
              isin: "IE00B3L10356",
              type: "interest"
            }),
            expect.objectContaining({
              amount: 1,
              asset: "ICS Sterling Liquidity Fund (Premier)",
              currency: "GBP",
              isin: "IE00B3L10356",
              side: "Buy",
              type: "interest reinvestment"
            })
          ])
        );
      });
    });

    describe("when the user has a settled savings topup", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
          consideration: { amount: 100, currency: "GBP" }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);

        expect(activity).toEqual([
          expect.objectContaining({
            amount: 1,
            asset: "ICS Sterling Liquidity Fund (Premier)",
            currency: "GBP",
            isin: "IE00B3L10356",
            side: "Buy",
            type: "order"
          })
        ]);
      });
    });

    describe("when the user has a settled savings withdrawal", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        await buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
          consideration: { amount: 100, currency: "GBP" }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);

        expect(activity).toEqual([
          expect.objectContaining({
            amount: 1,
            asset: "ICS Sterling Liquidity Fund (Premier)",
            currency: "GBP",
            isin: "IE00B3L10356",
            side: "Sell",
            type: "order"
          })
        ]);
      });
    });

    describe("when user has a pending reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          updatedAt: new Date(),
          status: "Pending",
          consideration: {
            currency: "GBP",
            amount: 1000
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the reward", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          updatedAt: new Date(),
          status: "Settled",
          consideration: {
            currency: "GBP",
            amount: 1000
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the reward", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "reward"
          })
        ]);
      });
    });

    describe("when user has a transaction that would be included but was created BEFORE the requested time period", () => {
      const TODAY = new Date("2024-01-10");

      let user: UserDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWithdrawalCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 30)
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the transaction", async () => {
        const activity = await UserService.getAccountStatementActivity(
          user.id,
          DateUtil.getDateOfDaysAgo(TODAY, 10)
        );
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a transaction that would be included but was created AFTER the requested time period", () => {
      const TODAY = new Date("2024-01-10");

      let user: UserDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWithdrawalCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 10)
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the transaction", async () => {
        const activity = await UserService.getAccountStatementActivity(
          user.id,
          DateUtil.getDateOfDaysAgo(TODAY, 30),
          DateUtil.getDateOfDaysAgo(TODAY, 20)
        );
        expect(activity).toEqual([]);
      });
    });
  });

  describe("updateDeviceToken", () => {
    it("should throw an error for an invalid platform", async () => {
      const platform = "invalidPlatform";
      const token = "validToken";
      const user = await buildUser({ deviceTokens: { ios: "oldToken" } });

      await expect(UserService.updateDeviceToken(user.id, platform as any, token)).rejects.toThrow(
        "Platform must be one of: android, ios"
      );
    });

    it("should update the device token for a valid platform and token", async () => {
      const platform = "ios";
      const token = "validToken";
      const user = await buildUser({ deviceTokens: { ios: "oldToken" } });

      await UserService.updateDeviceToken(user.id, platform, token);

      const updatedUser = await User.findById(user.id);
      expect(updatedUser.deviceTokens[platform]).toBe(token);
    });
  });

  describe("removeDeviceToken", () => {
    it("should throw an error for an invalid platform", async () => {
      const platform = "invalidPlatform";
      const user = await buildUser({ deviceTokens: { ios: "oldToken" } });

      await expect(UserService.removeDeviceToken(user.id, platform as any)).rejects.toThrow(
        "Platform must be one of: android, ios"
      );
    });

    it("should remove the device token for a valid platform", async () => {
      const platform = "ios" as PlatformType;
      const token = "validToken";
      const user = await buildUser({ deviceTokens: { ios: "oldToken" } });

      // First, update the device token
      await UserService.updateDeviceToken(user.id, platform, token);

      // Then, remove the device token
      await UserService.removeDeviceToken(user.id, platform);

      const updatedUser = await User.findById(user.id);
      expect(updatedUser.deviceTokens[platform]).toBeUndefined();
    });
  });
});
