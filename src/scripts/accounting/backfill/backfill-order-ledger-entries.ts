import { Order, OrderPopulationFieldsEnum } from "../../../models/Order";
import { TransactionPopulationFieldsEnum } from "../../../models/Transaction";
import { AccountingRecordIndex } from "../../../models/AccountingRecordIndex";
import { InvoiceReferenceNumber } from "../../../models/InvoiceReferenceNumber";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry
} from "../../../external-services/accountingLedgerStorageService";
import {
  LedgerAccounts,
  AccountingClientSegment,
  AccountingEventType,
  AccountingEntry
} from "../../../types/accounting";
import { clientSegmentToLedgerAccountMapping } from "../../../configs/accountingConfig";
import { UserDocument } from "../../../models/User";
import Decimal from "decimal.js";

interface MissingEntryInfo {
  stage: string;
  entries: AccountingEntry[];
  description: string;
  articleDate: string;
  entryType: "movements" | "revenues" | "expenses";
}

interface OrderAnalysis {
  orderId: string;
  amount: number;
  userId: string;
  clientSegment: AccountingClientSegment;
  clientLedgerAccount: LedgerAccounts;
  side: "Buy" | "Sell";
  isin: string;
  isMatched: boolean;
  fees: {
    fx: number;
    realtimeExecution: number;
    whCommission: number;
    brokerFee: number;
  };
  existingLedgerEntries: any[];
  missingEntries: MissingEntryInfo[];
}

const fixOrderLedgerEntries = async (orderId: string, dryRun: boolean = true) => {
  try {
    console.info(`Starting ledger entry fix for order: ${orderId} (dry-run: ${dryRun})`);

    // 1. Fetch the order and populate necessary fields
    const order = await Order.findById(orderId).populate([OrderPopulationFieldsEnum.TRANSACTION]);

    if (!order) {
      console.error(`Order with ID ${orderId} not found`);
      return;
    }

    if (order.consideration.currency !== "EUR") {
      console.error(`Order ${orderId} is not in EUR currency. Only EUR orders are supported.`);
      return;
    }

    // Populate the transaction owner
    const transaction = order.transaction as any;
    if (!transaction.populated("owner")) {
      await transaction.populate(TransactionPopulationFieldsEnum.OWNER);
    }

    const user = transaction.owner as UserDocument;
    if (!user.isEuropeanEntity) {
      console.error(`Order ${orderId} belongs to non-European entity. Only European entities are supported.`);
      return;
    }

    // Only process orders linked to supported transaction categories
    if (
      ![
        "AssetTransaction",
        "RebalanceTransaction",
        "SavingsTopupTransaction",
        "SavingsWithdrawalTransaction"
      ].includes(transaction.category)
    ) {
      console.error(`Order ${orderId} is linked to unsupported transaction category: ${transaction.category}`);
      return;
    }

    console.info(`Analyzing order: ${orderId}`);
    console.info(`- Amount: €${(order.consideration.amount / 100).toFixed(2)}`);
    console.info(`- Side: ${order.side}`);
    console.info(`- ISIN: ${order.isin}`);
    console.info(`- User: ${user._id}`);
    console.info(`- Client Segment: ${user.accountingClientSegment}`);
    console.info(`- Is Matched: ${order.isMatched}`);

    // 2. Analyze the order to determine what entries should exist
    const analysis = await analyzeOrder(order, user);

    // 3. Check what ledger entries already exist
    const existingEntries = await AccountingLedgerStorageService.queryLedgerEntriesByTransactionId(orderId);
    analysis.existingLedgerEntries = existingEntries;

    console.info("\n=== ORDER ANALYSIS ===");
    console.info(`Order Side: ${analysis.side}`);
    console.info(`Is Matched: ${analysis.isMatched ? "✅" : "❌"}`);
    console.info(`Existing Ledger Entries: ${existingEntries.length}`);

    if (existingEntries.length > 0) {
      console.info("\n--- Existing Ledger Entries ---");
      existingEntries.forEach((entry, index) => {
        console.info(
          `${index + 1}. ${entry.account_code} ${entry.side} €${entry.amount.toFixed(2)} (AA: ${entry.aa})`
        );
      });
    }

    // 4. Determine missing entries
    const missingEntries = determineMissingEntries(analysis);

    if (missingEntries.length === 0) {
      console.info(`\n🎉 No missing ledger entries found for order ${orderId}`);
      return;
    }

    console.info("\n=== MISSING ENTRIES ANALYSIS ===");
    console.info(`Found ${missingEntries.length} missing entry group(s):`);

    missingEntries.forEach((missingEntry, index) => {
      console.info(`\n--- Missing Entry Group ${index + 1}: ${missingEntry.stage} ---`);
      console.info(`Type: ${missingEntry.entryType}`);
      console.info(`Description: ${missingEntry.description}`);
      console.info(`Article Date: ${missingEntry.articleDate}`);
      console.info("Entries:");
      missingEntry.entries.forEach((entry, entryIndex) => {
        console.info(`  ${entryIndex + 1}. ${entry.account} ${entry.type} €${(entry.amount / 100).toFixed(2)}`);
      });
    });

    // 5. Create entries if not in dry-run mode
    if (!dryRun) {
      console.info("\n=== CREATING MISSING ENTRIES ===");
      await createMissingLedgerEntries(orderId, missingEntries, user._id.toString());
      console.info(`✅ Successfully created ${missingEntries.length} missing entry group(s)`);
    } else {
      console.info("\n💡 This was a dry-run. To actually create the entries, run with --no-dry-run");
    }

    console.info(`\n🎯 Analysis completed for order ${orderId}\n`);
  } catch (error) {
    console.error("Error fixing order ledger entries:", { data: { error: error.toString() } });
  }
};

async function analyzeOrder(order: any, user: UserDocument): Promise<OrderAnalysis> {
  const amount = order.consideration.amount ?? 0;
  const clientSegment = user.accountingClientSegment;
  const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];
  const side = order.side;
  const isin = order.isin;
  const isMatched = order.isMatched;

  // Extract fees (convert from euros to cents where needed)
  const fxFeeEuros = order.fees?.fx?.amount || 0;
  const realtimeExecutionFeeEuros = order.fees?.realtimeExecution?.amount || 0;
  const brokerFeeEuros = order.providers?.wealthkernel?.accountingBrokerFxFee || 0;

  const fees = {
    fx: Decimal.mul(fxFeeEuros, 100).toNumber(), // Convert to cents
    realtimeExecution: Decimal.mul(realtimeExecutionFeeEuros, 100).toNumber(), // Convert to cents
    whCommission: Decimal.add(fxFeeEuros, realtimeExecutionFeeEuros).mul(100).toNumber(), // Total WH commission in cents
    brokerFee: Decimal.mul(brokerFeeEuros, 100).toNumber() // Convert to cents
  };

  const missingEntries: MissingEntryInfo[] = [];

  // Only generate entries if order is matched (similar to accounting service logic)
  if (isMatched) {
    const netAmount = Decimal.sub(amount, fees.brokerFee).toNumber();

    // Main Asset Movement - movements
    const movements: AccountingEntry[] = [];
    if (side === "Buy") {
      movements.push(
        { account: clientLedgerAccount, amount: netAmount, type: "debit" },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: netAmount, type: "credit" },
        { account: LedgerAccounts.ASSETS_ACTIVE, amount: netAmount, type: "debit" },
        { account: LedgerAccounts.ASSETS_PASSIVE, amount: netAmount, type: "credit" }
      );
    } else if (side === "Sell") {
      movements.push(
        { account: LedgerAccounts.ASSETS_PASSIVE, amount: netAmount, type: "debit" },
        { account: LedgerAccounts.ASSETS_ACTIVE, amount: netAmount, type: "credit" },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: netAmount, type: "debit" },
        { account: clientLedgerAccount, amount: netAmount, type: "credit" }
      );
    }

    if (movements.length > 0) {
      missingEntries.push({
        stage: `${side} Order - Asset Movement`,
        entries: movements,
        description: getAccountingActivityDescription(
          user._id.toString(),
          order.id,
          side === "Buy" ? AccountingEventType.ASSET_BUY : AccountingEventType.ASSET_SELL,
          isin
        ),
        articleDate: new Date(order.filledAt).toISOString().slice(0, 10),
        entryType: "movements"
      });
    }

    // Total Commission Revenue (WH commission + broker commission)
    // Always generate revenue entries to ensure reference number is created, even if commission is 0
    const totalCommission = Decimal.add(fees.whCommission, fees.brokerFee).toNumber();
    const revenueEntries: AccountingEntry[] = [
      { account: clientLedgerAccount, amount: totalCommission, type: "debit" },
      { account: LedgerAccounts.COMMISSION_FEES_WH, amount: totalCommission, type: "credit" }
    ];

    missingEntries.push({
      stage: `${side} Order - Total Commission Revenue`,
      entries: revenueEntries,
      description: getAccountingActivityDescription(
        user._id.toString(),
        order.id,
        side === "Buy" ? AccountingEventType.ASSET_BUY : AccountingEventType.ASSET_SELL,
        isin
      ),
      articleDate: new Date(order.filledAt).toISOString().slice(0, 10),
      entryType: "revenues"
    });

    // Broker Fee Expense (only if broker fee > 0)
    if (fees.brokerFee > 0) {
      const expenseEntries: AccountingEntry[] = [
        { account: LedgerAccounts.BROKER_FEE_EXPENSE, amount: fees.brokerFee, type: "debit" },
        { account: LedgerAccounts.PAYABLES_TO_BROKER, amount: fees.brokerFee, type: "credit" },
        { account: LedgerAccounts.PAYABLES_TO_BROKER, amount: fees.brokerFee, type: "debit" },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: fees.brokerFee, type: "credit" }
      ];

      missingEntries.push({
        stage: `${side} Order - Broker Fee Expense`,
        entries: expenseEntries,
        description: getAccountingActivityDescription(
          user._id.toString(),
          order.id,
          side === "Buy" ? AccountingEventType.ASSET_BUY : AccountingEventType.ASSET_SELL,
          isin
        ),
        articleDate: new Date(order.filledAt).toISOString().slice(0, 10),
        entryType: "expenses"
      });
    }
  }

  return {
    orderId: order.id,
    amount,
    userId: user._id.toString(),
    clientSegment,
    clientLedgerAccount,
    side,
    isin,
    isMatched,
    fees,
    existingLedgerEntries: [],
    missingEntries
  };
}

function determineMissingEntries(analysis: OrderAnalysis): MissingEntryInfo[] {
  const { missingEntries, existingLedgerEntries } = analysis;
  const actuallyMissingEntries: MissingEntryInfo[] = [];

  // For each expected entry group, check if it already exists in the ledger
  for (const expectedGroup of missingEntries) {
    // Check if all entries in this group already exist
    const allEntriesExist = expectedGroup.entries.every((expectedEntry) => {
      return existingLedgerEntries.some((existingEntry) => {
        const expectedAmountEuros = Decimal.div(expectedEntry.amount, 100).toNumber();
        const amountMatches = Math.abs(existingEntry.amount - expectedAmountEuros) < 0.01;
        const accountMatches = existingEntry.account_code === expectedEntry.account;
        const sideMatches = existingEntry.side === expectedEntry.type;

        return amountMatches && accountMatches && sideMatches;
      });
    });

    if (!allEntriesExist) {
      actuallyMissingEntries.push(expectedGroup);
    }
  }

  return actuallyMissingEntries;
}

async function createMissingLedgerEntries(
  orderId: string,
  missingEntries: MissingEntryInfo[],
  userId: string
): Promise<void> {
  for (const missingEntry of missingEntries) {
    console.info(`Creating entries for: ${missingEntry.stage} (${missingEntry.entryType})`);

    // Create AccountingRecordIndex for this entry group
    const recordIndex = await new AccountingRecordIndex({
      linkedDocumentId: orderId,
      sourceDocumentType: "Order"
    }).save();

    console.info(`Created AccountingRecordIndex with AA: ${recordIndex.aaIndex}`);

    // Create InvoiceReferenceNumber for revenues (commission income)
    let invoiceReferenceNumber: string | undefined;
    if (missingEntry.entryType === "revenues") {
      const invoiceReference = await new InvoiceReferenceNumber({
        linkedDocumentId: orderId,
        sourceDocumentType: "Order"
      }).save();
      invoiceReferenceNumber = invoiceReference.invoiceId.toString();
      console.info(`Created InvoiceReferenceNumber with ID: ${invoiceReference.invoiceId}`);
    }

    // Transform entries to ledger format
    const ledgerEntries: AccountingLedgerEntry[] = missingEntry.entries.map((entry) => ({
      aa: recordIndex.aaIndex,
      account_code: entry.account,
      side: entry.type,
      amount: Decimal.div(entry.amount, 100).toNumber(), // Convert cents to euros
      reference_number: invoiceReferenceNumber as any,
      article_date: missingEntry.articleDate,
      description: missingEntry.description,
      document_id: orderId,
      owner_id: userId
    }));

    // Add to ledger
    const result = await AccountingLedgerStorageService.addValidatedLedgerEntries(ledgerEntries);

    if (result.success) {
      console.info(`✅ Successfully created ${ledgerEntries.length} ledger entries for ${missingEntry.stage}`);
    } else {
      console.error(`❌ Failed to create ledger entries for ${missingEntry.stage}: ${result.error}`);
    }
  }
}

function getAccountingActivityDescription(
  userId: string,
  orderId: string,
  eventType: AccountingEventType,
  isin?: string
): string {
  return `${userId}|${orderId}${isin ? `|${isin}` : ""}|${eventType}`;
}

// Main execution
(async () => {
  try {
    const args = process.argv.slice(2);

    if (args.length === 0) {
      console.error("Usage: node fix-order-ledger-entries.ts <orderId> [--no-dry-run]");
      console.error("Example: node fix-order-ledger-entries.ts 507f1f77bcf86cd799439011");
      console.error("Example: node fix-order-ledger-entries.ts 507f1f77bcf86cd799439011 --no-dry-run");
      process.exit(1);
    }

    const orderId = args[0];
    const dryRun = !args.includes("--no-dry-run");

    if (!orderId.match(/^[0-9a-fA-F]{24}$/)) {
      console.error("Invalid order ID format. Must be a valid MongoDB ObjectId.");
      process.exit(1);
    }

    await fixOrderLedgerEntries(orderId, dryRun);
    process.exit(0);
  } catch (error) {
    console.error("Script execution failed:", error);
    process.exit(1);
  }
})();
